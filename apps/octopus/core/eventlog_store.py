import enum
import logging
import base64
import json
from typing import List, Tuple
from entity.deps.entity import BaseEntity
from resx.obs_client import OBSClient
from libs.concurrent import BoundedExecutor
from apps.octopus.core.constants import OCTOPUS
from apps.octopus.core.eventlog import Eventlog, EventlogCode

logger = logging.getLogger(__file__)


class EventlogStorePos(int, enum.Enum):
    SCHEDULED = 1  # 已调度
    CRAWLED = 3  # 已抓取
    PARSED = 6  # 已解析
    FEEDBACK = 9  # 已回收

    def human(self) -> str:
        if self == EventlogStorePos.SCHEDULED:
            return '待抓取'
        if self == EventlogStorePos.CRAWLED:
            return '已抓取'
        if self == EventlogStorePos.PARSED:
            return '已解析'
        if self == EventlogStorePos.FEEDBACK:
            return '已回收'
        return self.name


class EventlogMeta(BaseEntity):
    event_id: str
    ts: int
    reason: str
    weight: int
    changed: bool
    status: EventlogStorePos
    code: EventlogCode
    cost: int
    try_id: int
    entry_name: str
    inst_name: str
    word: str


class EventlogStore(object):
    def __init__(self, max_workers=20):
        self.obs = OBSClient()
        # self.redis_queue = RedisQueue(name=f'{OCTOPUS}:eventlog_store', max_length=1024, **OCTOPUS_REDIS)
        self.processor = BoundedExecutor(max_workers=max_workers)
        self.dir = f'{OCTOPUS}'

    def save(self, eventlog: Eventlog, pos: EventlogStorePos):
        entry_name = eventlog.selector.entry_name
        word = eventlog.selector.word
        eventlog_meta = EventlogMeta.from_dict({
            'ts': eventlog.selector.send_ts,
            'event_id': eventlog.event_id,
            'reason': eventlog.selector.reason,
            'weight': eventlog.selector.weight,
            'changed': eventlog.spider.spider_data.get('msv_changed', False),
            'status': pos,
            'code': eventlog.code,
            'cost': max(eventlog.selector.receive_ts - eventlog.selector.send_ts, -1),
            'try_id': eventlog.selector.try_id,
            'entry_name': eventlog.selector.entry_name,
            'inst_name': eventlog.selector.inst_name,
            'word': eventlog.selector.word,
        })
        file_name = base64.b16encode(eventlog_meta.to_json().encode('utf8')).decode()
        path = f'{self.dir}/{entry_name}/{word}/{file_name}.json'
        self.processor.submit(self.obs.put, path, eventlog.to_json())

    def read(self, entry_name, word) -> List[Tuple[EventlogMeta, str]]:
        file_info_list: List[Tuple[EventlogMeta, str]] = []
        for file in self.obs.list(prefix=f'{self.dir}/{entry_name}/{word}/'):
            encoded_file_info = file.split('/')[-1][:-len('.json')]
            file_info = json.loads(base64.b16decode(encoded_file_info).decode('utf8'))
            eventlog_meta = EventlogMeta.from_dict(file_info)
            file_info_list.append((eventlog_meta, encoded_file_info))
        return file_info_list

    def read_content(self, encoded_file_info):
        file_info = json.loads(base64.b16decode(encoded_file_info).decode('utf8'))
        eventlog_meta = EventlogMeta.from_dict(file_info)
        path = f'{self.dir}/{eventlog_meta.entry_name}/{eventlog_meta.word}/{encoded_file_info}.json'
        data = self.obs.get(path)
        return data
