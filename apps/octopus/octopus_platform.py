import json
import time
from typing import Optional
from datetime import timedelta, datetime

import requests

from libs.dt import cur_ts_sec
from libs.log2 import setup_logger
from libs.env import get_stack_info
from gslib.credit_code import credit_code_valid
from dao.octopus.platform import PlatformTaskDao, TaskType, TaskStatus
from dao.company import CompanyDao, Company
from dao.company_hk import CompanyHkDao, CompanyHk
from apps.octopus.core.constants import PLATFORM_REASON
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger(use_file_log=True, app_name='octopus_platform', backup_count=9, rotate_mode='D')
platform_task_dao = PlatformTaskDao()
company_dao = CompanyDao()
company_hk_dao = CompanyHkDao()
entry_manager = EntryManager()


def get_entry_word_credit(param) -> Optional[str]:
    if credit_code_valid(param):
        return param
    for item in company_dao.get_many(value=param, field='name'):
        c: Company = item
        if credit_code_valid(c.credit_code):
            return c.credit_code
    return None


def main():
    for loop_id in range(cur_ts_sec(), int(1e20)):
        for task in platform_task_dao.get_tasks():
            if task.status == TaskStatus.CREATE:
                params = {}
                # if task.task_type == TaskType.BRNO_HK:
                #     entry_name, inst_name, entry_word = 'brno', 'hk', task.param
                #     company_hk: CompanyHk = company_hk_dao.get(value=task.param, field='company_num')
                #     if company_hk and company_hk.br_num:
                # #         entry_word = company_hk.br_num
                # elif task.task_type == TaskType.CHINA_NPO:
                #     entry_name, inst_name = 'credit', 'china_npo'
                #     entry_word = get_entry_word_credit(task.param)
                if task.task_type == TaskType.CREDIT_CHINA:
                    entry_name, inst_name = 'credit', 'credit_china'
                    entry_word = get_entry_word_credit(task.param)
                # elif task.task_type == TaskType.GDS:
                #     entry_name, inst_name = 'credit', 'gds'
                #     tokens = task.param.split(',')
                #     entry_param = tokens[0]
                #     if len(tokens) == 2:
                #         params['barcode'] = tokens[1]
                #     entry_word = get_entry_word_credit(entry_param)
                # elif task.task_type == TaskType.CODS:
                #     entry_name, inst_name = 'credit', 'cods'
                #     entry_word = get_entry_word_credit(task.param)
                # elif task.task_type == TaskType.ACFTU:
                #     entry_name, inst_name = 'credit', 'acftu'
                #     entry_word = get_entry_word_credit(task.param)
                # elif task.task_type == TaskType.INSTITUTION_GJ:
                #     entry_name, inst_name = 'credit', 'institution_gj'
                #     entry_word = get_entry_word_credit(task.param)
                # elif task.task_type == TaskType.LAW_FIRM_GJ:
                #     entry_name, inst_name = 'credit', 'law_firm_gj'
                #     entry_word = get_entry_word_credit(task.param)
                elif task.task_type in [TaskType.HIDE_REPORT_PHONE, TaskType.HIDE_REPORT_POSTAL]:
                    tokens = task.param.split(',')
                    if len(tokens) != 2:
                        platform_task_dao.set_task(task.id, status=TaskStatus.FAILED, record='输入格式错误')
                        continue
                    post_data = [
                        {
                            'company_name': tokens[0],
                            'hide_user': task.belong,
                            'hide_type': '年报屏蔽电话' if TaskType == TaskType.HIDE_REPORT_PHONE else '年报屏蔽邮箱',
                            'hide_content': tokens[1],
                        }
                    ]
                    try:
                        resp = requests.post(
                            url='http://gs-api.jindidata.com/hide_data',
                            timeout=5,
                            json=post_data,
                            verify=False,
                        )
                    except Exception as e:
                        logger.warning(f'fail request hide report e={e} {get_stack_info()} post_data={post_data}')
                        platform_task_dao.set_task(task.id, status=TaskStatus.FAILED, record='接口错误')
                        continue
                    if resp.status_code != 200 or resp.json().get('code') != 2000:
                        logger.warning(f'fail request hide report {resp.text} {resp.status_code} post_data={post_data}')
                        platform_task_dao.set_task(task.id, status=TaskStatus.FAILED, record='接口错误')
                        continue
                    platform_task_dao.set_task(task.id, status=TaskStatus.SUCCESS, record='')
                    continue
                else:
                    if task.task_type != TaskType.OTHERS:
                        logger.warning(f'no handle for task={task}')
                    continue
                if not entry_word:
                    platform_task_dao.set_task(task.id, status=TaskStatus.FAILED, record='名称不存在')
                    continue

                # entry不存在会自动创建 params不允许覆盖
                suc = entry_manager.inst_immediate(
                    entry_name=entry_name,
                    entry_word=entry_word,
                    inst_name=inst_name,
                    reason=PLATFORM_REASON,
                    params=params | {'platform_id': task.id},
                    diff_params_ret_fail=False,
                    ignore_latest_search_empty=False,
                )
                if not suc:
                    logger.warning(f'inst_immediate fail task={task}')
                    platform_task_dao.set_task(task.id, status=TaskStatus.FAILED, record='标记立即调度失败')
                else:
                    logger.warning(f'inst_immediate success task={task}')
                    platform_task_dao.set_task(task.id, status=TaskStatus.EXECUTE, record='标记立即调度成功')
            elif task.status == TaskStatus.EXECUTE:
                if task.task_type != TaskType.OTHERS:
                    if task.update_time + timedelta(hours=1) < datetime.now():
                        platform_task_dao.set_task(task.id, TaskStatus.FAILED, record='执行时长超过1小时')
                        logger.info(f'expired task={task}')
            else:
                pass
        logger.info(f'loop_id={loop_id} sleep for...')
        time.sleep(5)


if __name__ == '__main__':
    main()
