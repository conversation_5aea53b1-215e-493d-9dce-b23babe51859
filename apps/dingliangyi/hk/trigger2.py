from resx.log import setup_logger

logger = setup_logger(logger_path='./logs/hk_trigger2.log', backup_count=10, name=__name__)
import time
from concurrent.futures import ThreadPoolExecutor, TimeoutError
from datetime import datetime
from threading import Lock
from typing import Dict

from requests import Session
from resx.config import *
from resx.log import setup_logger
from resx.mysql_dao import MySQLDao
from resx.redis_types import Redis
from zhconv import convert

from apps.dingliangyi.hk.inv_id import get_gid
from apps.spider.crawler import CrawlerHK2
from apps.spider.crawler.crawler import MyException, Crawler
from apps.spider.parser.parser_HK import ParserHK
from apps.spider.utils.obs_manager import OBSManager
from resx.obs_client import OBSClient
from dao.company_graph import CompanyGraphDao
from dao.company_hk import CompanyHk
from dao.hk.hk_annual_capital import HkAnnualCapital
from dao.hk.hk_annual_shareholders import HkAnnualShareholders
from dao.hk.hk_company_secretary_particulars_new import HkCompanySecretaryParticularsNew
from dao.hk.hk_directors_new import HkDirectorsNew
from dao.hk.hk_image_list import HkImageList
from dao.hk.hk_pdf_download_record import HkPdfDownloadRecordDao
from libs.feishu import send_feishu_message_attachments as feishu

redis = Redis(**CFG_REDIS_GS, db=3)
dao = MySQLDao(db_tb_name='biz.hk_company_payed_report', **{
    'host': 'rm-2zei227x9ut1ovvkt35930.mysql.rds.aliyuncs.com',
    'user': 'jindi',
    'password': 'JjiniTicket4code'
})


class Pay(CrawlerHK2, ParserHK):
    def __init__(self):
        self.sessions: Dict[int, Session] = dict()
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }
        self.timeout = 10
        self.lock = Lock()
        self.company_hk_dao = MySQLDao(db_tb_name='prism.company_hk', **CFG_MYSQL_GS_OUTER, primary_index_fields=(['br_num'], []),
                                       entity_class=CompanyHk, ignore_fields=['id', 'company_id', 'updatetime'])
        self.hk_pdf_downloadRecord_dao = HkPdfDownloadRecordDao()
        self.obs = OBSClient(bucket_name='jindi-oss-gsxt')
        self.obs_manager = OBSManager()
        self.company_graph_dao = CompanyGraphDao()

        self.share_dao = MySQLDao(db_tb_name='prism.hk_annual_capital', **CFG_MYSQL_GS_OUTER, primary_index_fields=([], []),
                                  entity_class=HkAnnualCapital, ignore_fields=['id', 'create_time'])
        self.shareholders_dao = MySQLDao(db_tb_name='prism.hk_annual_shareholders', **CFG_MYSQL_GS_OUTER,
                                         primary_index_fields=(['hk_id'], ['shareholder_name']),
                                         entity_class=HkAnnualShareholders, ignore_fields=['id', 'create_time'])
        self.secretary_dao = MySQLDao(db_tb_name='prism.hk_company_secretary_particulars_new', **CFG_MYSQL_GS_OUTER,
                                      primary_index_fields=(['hk_id'], ['name_english', 'name_chinese', 'cr_no', 'passport_no']),
                                      entity_class=HkCompanySecretaryParticularsNew, ignore_fields=['id', 'create_time'])
        self.directors_dao = MySQLDao(db_tb_name='prism.hk_directors_new', **CFG_MYSQL_GS_OUTER,
                                      primary_index_fields=(['hk_id'], ['name_english', 'name_chinese', 'cr_no', 'passport_no']),
                                      entity_class=HkDirectorsNew, ignore_fields=['id', 'create_time'])
        self.image_dao = MySQLDao(db_tb_name='prism.hk_image_list', **CFG_MYSQL_GS_OUTER,
                                  primary_index_fields=(['hk_id'], ['image_id_str']),
                                  entity_class=HkImageList, ignore_fields=['id', 'create_time'])

    def run(self, keyword: str, id_type: int):

        for _ in range(3):
            try:
                return self.task(keyword, id_type)
            except MyException as e:
                if e.message == '网站维护中':
                    logger.info(f'{keyword}:{id_type} 网站维护中，等待9分钟')
                    time.sleep(20)
                    return 0
                self.sessions = {}
            except Exception as e:
                logger.error(f'{keyword}:{id_type} error: {e}')
                Crawler.custom_traceback(e)
                self.sessions = {}

        return 3

    def task(self, keyword: str, id_type: int):

        login_success = False
        for _ in range(5):
            if self.keep_alive(keyword):
                login_success = True
                break
            if self.Log_in(True, keyword):
                if not self.keep_alive(keyword, num=2):
                    continue
                login_success = True
                break

        if not login_success:
            logger.info('登录失败')
            raise MyException('登录失败')

        # 获取余额
        balance = self.account_balance(keyword)
        if float(balance) < 100:
            logger.info(f'{keyword} 余额不足: {balance}')
            feishu(chat_id='oc_c1a20434dc7c3ca4c6fc279ddfaeb879', ats=['丁良益'], text=f'主账号余额不足: {balance}，请充值,程序已暂停')
            exit(0)

        # 每日余额通知功能 - 每天只发送一次
        today = datetime.now().strftime('%Y-%m-%d')
        daily_notice_key = f'hk_daily_balance_notice:{today}'
        # 检查今天是否已经发送过余额通知
        if not redis.exists(daily_notice_key):
            # 发送每日余额通知
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            notice_text = f'【每日余额通知】\n当前账户余额: {balance}\n通知时间: {current_time}'
            try:
                feishu(chat_id='oc_c1a20434dc7c3ca4c6fc279ddfaeb879', ats=[''], text=notice_text)
                # 设置Redis标记，过期时间25小时（避免时区问题）
                redis.setex(daily_notice_key, 90000, '1')
                logger.info(f'每日余额通知已发送: {balance}, 时间: {current_time}')
            except Exception as e:
                logger.error(f'发送每日余额通知失败: {e}')
        else:
            logger.debug(f'今日余额通知已发送过，跳过发送')

        if id_type == 1:
            gid_cid = self.company_graph_dao.get_by_gid(keyword)
            hk = self.company_hk_dao.get(company_id=gid_cid.cid)
            br_no = hk.br_num
            hk_id = hk.id

            if self.if_update(hk_id, br_no):
                refNo = self.pay({'brNo': br_no}, 1, br_no)
                share = self.ps_company_particulars_details_share_capital(refNo, br_no)
                secretary = self.ps_company_particulars_details_company_secretary(refNo, br_no)
                directors = self.ps_company_particulars_details_directors_list(refNo, br_no)
                shareholder = self.ps_company_particulars_details_shareholder_information(refNo, br_no)
                change = self.save_data(hk_id, share, secretary, directors, shareholder, br_no)
                return 11 if change else 1
            else:
                return 1

        elif id_type == 2:
            pdf = self.obs.get_content('hk_pdf/{}.pdf'.format(keyword))
            if pdf:
                return 11
            doc = self.image_dao.get(image_id_str=keyword)
            hk = self.company_hk_dao.get(id=doc.hk_id)
            br_no = hk.br_num
            base_info = self.search_by_brno(br_no)

            parm = {'barcode': keyword, 'brNo': br_no, 'coyTyp': base_info['coyTyp']}
            refNo = self.pay(parm, 3, br_no)
            pdf = self.ps_document_index_downloads(refNo)
            self.obs.put(f'hk_pdf/{keyword}.pdf', pdf)
            self.hk_pdf_downloadRecord_dao.i_u_by_any(**{'record_id': keyword, 'obs_url': f'hk_pdf/{keyword}.pdf', 'pdf_source': 2})
            return 11

    def if_update(self, hk_id, br_no):
        # 抓一次
        if_ = redis.sismember('hk_br_num', br_no)
        if if_:
            redis.srem('hk_br_num', br_no)
            return True

        lisr_ = self.ps_doc_list(br_no)
        # self.save_image(lisr_, hk_id)
        last_crawl_time = self.last_crawl_time(hk_id)
        if not last_crawl_time:
            return True
        for i in lisr_:
            filing_date = datetime.strptime(i['filingDateForDisplay'], "%d-%b-%Y %H:%M") if (
                    i.get('filingDateForDisplay') and i['filingDateForDisplay'] != '-') else None
            if filing_date and filing_date > last_crawl_time:
                return True

    def last_crawl_time(self, hk_id):
        pre = self.share_dao.get(hk_id=hk_id)
        if pre:
            return pre.update_time

    def ps_doc_list(self, br_no):
        url = "https://www.e-services.cr.gov.hk/ICRIS3ES/ps/document-index/count.do"
        data = {"actvyCode": "S-DISAA", "brNo": br_no, "coyTyp": "I", "entryActvyCode": "S-DISAA-EN", "group": "ALL",
                "listSearchActvyCode": "S-DISAA-P1", "pageIndex": 1, "pageSize": 50, "yearSearchType": 0}
        res = self.request("POST", url, json=data, tojson=True, name='count.do')
        if not res.get('data'):
            return []
        count = res['data']

        image = {
            'ALL': 'ALL'
        }
        image_list = []
        for k, v in image.items():
            url = "https://www.e-services.cr.gov.hk/ICRIS3ES/ps/document-index/list-search.do"
            data = {"group": k, "yearSearchType": 0, "brNo": br_no, "pageSize": count, "pageIndex": 1}
            res = self.request("POST", url, json=data, tojson=True, name=f'list-search.do-{v}')
            if res['data']:
                image_list.extend(res['data'])
        return image_list

    def keep(self):
        res = self.request("GET", url="https://www.e-services.cr.gov.hk/ICRIS3EP/system/login/sync/user.do", tojson=True)
        if res.get('successful'):
            return True

    def save_image(self, image_list, hk_id):
        for image in image_list:
            info = {
                'hk_id': hk_id,
                'image_id_str': image['docID'],
                'image_id': 0,
                'image_name': image['docChNameDisplay'],
                'image_name_s': convert(image['docChNameDisplay'], "zh-hans"),
                'filing_date': datetime.strptime(image['filingDate'], "%d-%b-%Y %H:%M") if image.get('filingDate') else None,
                'pages': image.get('noOfPg'),
                'size': image.get('fileSize'),
                'state': '可供查阅' if 'AVAILABLE' in image['statusForDisplay'] else '不可查阅',
                'image_type': '周年申报表及帐目 / 财务报表'
            }
            self.image_dao.save(HkImageList.from_dict(info))

    def save_data(self, hk_id, share, secretary, directors, shareholder, br_no):
        self.obs_manager.upload_pages(
            pages={
                'share.txt': share,
                'secretary.txt': secretary,
                'directors.txt': directors,
                'shareholder.txt': shareholder
            },
            bak_count=10,
            base_dir=f'page/hk_pay/{br_no}',
        )

        if_change = False

        # 股权
        # 删除历史数据
        pre_share = list(self.share_dao.get_many(hk_id=hk_id))
        self.share_dao.execute('delete from prism.hk_annual_capital where hk_id = %s', (hk_id,))
        share_list: list[dict] = []
        for i in range(0, len(share), 2):
            pair = share[i:i + 2]
            info = {
                'hk_id': hk_id,
                'shares_class': '',
                'currency': pair[0]['currCode'],
                'issued_total_number': 0,
                'issued_total_amount': float(pair[0]['amt']),
                'issued_total_amount_paid': float(pair[1]['amt']) if len(pair) == 2 else ''
            }
            share_list.append(info)
            sql = f"insert into prism.hk_annual_capital set {', '.join(f'{f}=%s' for f in info.keys())}"
            self.share_dao.execute(sql, list(info.values()))

        if len(pre_share) == len(share_list):
            for idx, i in enumerate(pre_share):
                for k, v in i.model_dump().items():
                    if k not in ['issued_total_amount']:
                        continue
                    if v != share_list[idx][k]:
                        if_change = True
                        break
        else:
            if_change = True

        # 秘书
        secretary_list = []
        if secretary['naturalPerson']:
            for naturalPerson in secretary['naturalPerson']:
                info = {
                    'hk_id': hk_id,
                    'name_english': self.select_name(naturalPerson['engSname'], naturalPerson['engOname']),
                    'name_chinese': naturalPerson['chiName'] if naturalPerson['chiName'] else '-',
                    'pre_used_name': ';'.join(naturalPerson['previousName']) or '-',
                    'another_name': ';'.join(naturalPerson['alias']) or '-',
                    'mailing_address': naturalPerson['address'],
                    'cr_no': naturalPerson['hkid'],
                    'passport_no': naturalPerson['pptNo'],
                    'passport_iss_country': self.select_name(naturalPerson['ctryTcName'], naturalPerson['ctryEnName']),
                    'appointment': self.timestamp2datetime(naturalPerson['apptDate']),
                    'import_note': naturalPerson['rmk'] or '-',
                    'entity_type': 'naturalPerson 自然人'
                }
                secretary_list.append(HkCompanySecretaryParticularsNew.from_dict(info))
        if secretary['bodyCorporate']:
            for bodyCorporate in secretary['bodyCorporate']:
                info = {
                    'hk_id': hk_id,
                    'name_english': bodyCorporate['engCoyName'],
                    'name_chinese': bodyCorporate['chiCoyName'] if bodyCorporate['chiCoyName'] else '-',
                    'cr_no': bodyCorporate['brno'],
                    'mailing_address': bodyCorporate['address'],
                    'appointment': self.timestamp2datetime(bodyCorporate['apptDate']),
                    'import_note': '-' if bodyCorporate['rmk'] is None else bodyCorporate['rmk'],
                    'entity_type': 'bodyCorporate 法人团体'
                }
                secretary_list.append(HkCompanySecretaryParticularsNew.from_dict(info))
        change1, change2, change3 = self.secretary_dao.save_group(secretary_list, [hk_id])
        if change1 + change2 + change3 > 0:
            if_change = True

        # 董事
        directors_list = []
        for director in directors:
            info = {
                'hk_id': hk_id,
                'name_english': director['engName'] if director['engName'] else '-',
                'name_chinese': director['chiName'] if director['chiName'] else '-',
                'cr_no': director['hkid'] if director['hkid'] else '-',
                'passport_no': director['pptNo'] if director['pptNo'] else '-',
                'passport_iss_country': f"{director['ctryTcName'] or ''} {director['ctryEnName'] or ''}".strip() or '-',
                'entity_type': '自然人' if director['directorType'] == 'I' else '法人团体'
            }
            directors_list.append(HkDirectorsNew.from_dict(info))
        change1, change2, change3 = self.directors_dao.save_group(directors_list, [hk_id])
        if change1 + change2 + change3 > 0:
            if_change = True

        # 股东
        shareholderList = []
        total = 0
        for i in shareholder['shareholderList']:
            total += int(i.get('shrHldNum'))
        for sha in shareholder['shareholderList']:
            name = sha.get('chiName') if sha.get('chiName') else ""
            name += ' ' + sha.get('engName') if sha.get('engName') else ""
            temp = {
                'hk_id': hk_id,
                'shareholder_name': name.strip(),
                'current_holding': str(sha.get('shrHldNum')),
                'address': sha.get('address'),
                'ratio': "{:.2%}".format(int(sha.get('shrHldNum')) / total),
                'remarks': sha.get('rmks') if sha.get('rmks') else '-',
                'shares_class': '普通股' if sha.get('clsShr') == 'Ordinary' else sha.get('clsShr'),
                'name_en': sha.get('engName') or "",
                'name_cn': sha.get('chiName') or "",
                'inv_id': get_gid(sha.get('chiName') or "", sha.get('engName') or ""),
            }
            shareholderList.append(HkAnnualShareholders.from_dict(temp))
        change1, change2, change3 = self.shareholders_dao.save_group(shareholderList, [hk_id])
        if change1 + change2 + change3 > 0:
            if_change = True

        return if_change


def run_with_timeout(pay, graph_image_id, id_type, timeout=60 * 3):
    with ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(pay.run, graph_image_id, id_type)
        try:
            result = future.result(timeout=timeout)
            return result
        except TimeoutError:
            logger.error(f"Task timeout for graph_image_id: {graph_image_id} id_type: {id_type}")
            future.cancel()
            return 3


def main():
    sql = 'select * from biz.hk_company_payed_report where report_status = 0 order by update_time'
    pay = Pay()
    logger.info('start')
    while True:
        task = dao.select(sql)
        if task:
            logger.info(f'{task["graph_image_id"]} - {task["id_type"]} 任务开始')
            dao.execute('update biz.hk_company_payed_report set report_status = 4 where id = %s', (task['id'],))
            result = run_with_timeout(pay, task['graph_image_id'], task['id_type'])
            dao.execute('update biz.hk_company_payed_report set report_status = %s where id = %s', (result, task['id']))
            logger.info(f'{task["graph_image_id"]} - {task["id_type"]} 任务结束 status: {result}')
        else:
            logger.info("Sleeping for 1s...")
            time.sleep(1)


if __name__ == '__main__':
    main()
    # pay = Pay()
    # pay.task('7124265936', 1)
