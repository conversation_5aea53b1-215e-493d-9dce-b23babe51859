import asyncio
import random
import re
from pathlib import Path
import argparse
import shutil
import json

import requests
from DrissionPage import Chromium, ChromiumOptions
from loguru import logger

from apps.dingliangyi.boss.process_manage import start_proxy_server, stop_proxy_server
from apps.dingliangyi.boss.proxy_switch import ProxySwitchClient
from gt_challege import verify_nine
from resx.config import *
from resx.redis_types import Redis


def copy_plugin(port=8675):
    try:
        base_dir = Path(__file__).resolve().parent.parent  # apps/dingliangyi
        src_dir = base_dir / 'switch_proxy_plugin'
        dst_root = base_dir / 'boss_plugins'
        dst_dir = dst_root / f'switch_proxy_plugin_{port}'

        if not src_dir.exists():
            logger.error(f"源插件目录不存在: {src_dir}")
            return None

        dst_root.mkdir(parents=True, exist_ok=True)

        # 如果目标已存在，先删除再复制，保证内容最新
        if dst_dir.exists():
            logger.info(f"目标目录已存在，先删除: {dst_dir}")
            shutil.rmtree(dst_dir)

        shutil.copytree(src_dir, dst_dir)
        logger.info(f"插件已复制到: {dst_dir}")

        # 更新 background.js 中的 WS 端口
        if update_websocket_url(str(dst_dir), port=port):
            logger.success(f"已更新 {dst_dir}/background.js 的 WebSocket 端口为 {port}")
        else:
            logger.warning(f"未能更新 {dst_dir}/background.js 的 WebSocket 端口")

        return str(dst_dir)
    except Exception as e:
        logger.error(f"复制插件失败: {e}")
        return None


def update_websocket_url(path, port: int = 8765):
    try:
        js_path = path + '/background.js'
        with open(js_path, 'r', encoding='utf-8') as f:
            content = f.read()

        new_url = f"ws://127.0.0.1:{port}"
        pattern = r"const\s+WS_SERVER_URL\s*=\s*['\"]ws://[^'\"]*['\"];"
        replacement = f"const WS_SERVER_URL = '{new_url}';"
        new_content, count = re.subn(pattern, replacement, content)

        with open(js_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        logger.info(f"成功更新WebSocket URL: {new_url} (替换了 {count} 处)")
        return True
    except Exception as e:
        logger.error(f"更新WebSocket URL时出错: {e}")
        return False


def set_proxy(client: ProxySwitchClient = None):
    async def async_set_proxy():
        try:
            await client.connect()
            success, message = await client.switch_proxy(host, port, "http")
            logger.info(f"Proxy switch result: {success}, message: {message}")
        finally:
            await client.disconnect()

    IP = get_long_proxy()['http']
    logger.info(f"Using proxy: {IP}")
    host, port = IP.split('//')[1].split(':')[0], int(IP.split(':')[2])
    asyncio.run(async_set_proxy())
    return IP


def get_long_proxy():
    res = requests.get('http://*************:8015/long-proxy')
    proxy = random.choice(res.text.split('\r\n'))
    return {
        'http': f'http://{proxy}',
        'https': f'http://{proxy}'
    }


def init_co(port=9222, wss_port=8675):
    co = ChromiumOptions().set_local_port(port)
    co.ignore_certificate_errors()
    co.set_load_mode('none')  # eager none
    co.set_argument('--no-sandbox')
    co.set_argument("--disable-gpu")
    # co.set_argument('--disable-features=DisableLoadExtensionCommandLineSwitch')
    co.add_extension(str(Path(__file__).resolve().parent.parent / 'boss_plugins' / f'switch_proxy_plugin_{wss_port}'))
    # co.set_argument('--disable-extensions-except',
    #                 str(Path(__file__).resolve().parent.parent / 'boss_plugins' / f'switch_proxy_plugin_{wss_port}'))
    co.set_browser_path('/Users/<USER>/Downloads/chrome-mac/Chromium.app/Contents/MacOS/Chromium')
    # co.set_browser_path('/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge')
    # co.incognito(True)
    # co.headless(True)
    # co.set_argument('--auto-open-devtools-for-tabs')  # 自动打开开发者工具
    return co


def get_tab(browser: Chromium, websocket_client):
    tab = browser.latest_tab
    tab.set.cookies.clear()
    # tab.run_cdp('Network.setCacheDisabled', cacheDisabled=True)
    # tab.run_cdp('Runtime.enable')
    # tab.run_cdp('Log.enable')
    # tab.set.load_mode('none')
    IP = set_proxy(websocket_client)  # 设置代理
    tab.set.user_agent(tab.user_agent.replace('Headless', ''))  # 去掉Headless
    tab.listen.start([
        'search/joblist.json',
        'captcha/gettype'
    ])
    return tab, IP


def boss(browser: Chromium, websocket_client):
    tab, IP = get_tab(browser, websocket_client)
    tab.get('https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB', timeout=5)

    packets = tab.listen.wait(2, fit_count=False, timeout=10)  # 等待数据包
    retry_count = 0
    max_retries = 3

    while not packets and retry_count < max_retries:
        retry_count += 1
        logger.info(f"No packets received, retrying... ({retry_count}/{max_retries})")
        tab.listen.stop()
        tab.listen.start()
        IP = set_proxy(websocket_client)  # 重新设置代理
        tab.get('https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB', timeout=5)
        packets = tab.listen.wait(2, fit_count=False, timeout=10)  # 等待数据包

    # 检查是否成功获取到数据包
    if not packets:
        logger.error("Failed to receive packets after maximum retries")
        return False

    for packet in packets:
        if 'captcha/gettype' in packet.url:
            tab.listen.stop()  # 停止监听
            if verify_nine(IP, packet.response.body['zpData']['startCaptcha']):
                tab.listen.start()
                tab.get('https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB', timeout=5)
                packets = tab.listen.wait(2, fit_count=False, timeout=10)
                # 再次检查验证后的数据包
                if not packets:
                    logger.error("Failed to receive packets after captcha verification")
                    return False
                break
        logger.info(f"Packet received: {packet.url} {packet.response.status} {packet.response.raw_body} {packet.request.cookies}")

    tab.stop_loading()  # 主动停止加载

    # 确保有足够的数据包用于提取cookies
    if len(packets) < 2:
        logger.error("Insufficient packets received for cookie extraction")
        return False

    cookies = {
        packet_cookie['name']: packet_cookie['value']
        for packet_cookie in packets[1].request.cookies
    }

    redis.rpush('boss_cookies', json.dumps({"IP": IP, "cookies": cookies}, ensure_ascii=False))
    logger.success('Browser session completed successfully.')


def process(port=8675, br=9222):
    process_info = start_proxy_server(port=port)
    try:
        copy_plugin(port)
        websocket_client = ProxySwitchClient(port)
        co = init_co(br, port)
        while True:
            try:
                browser = Chromium(addr_or_opts=co)
                boss(browser, websocket_client)
            except Exception as e:
                logger.error(f'任务失败: {e}，重试中...')
    except KeyboardInterrupt:
        stop_proxy_server(process_info)


if __name__ == '__main__':
    redis = Redis(**CFG_REDIS_GS, db=3)
    parser = argparse.ArgumentParser(description="Boss Cookie Refresher")
    parser.add_argument('--port', type=int, default=8675, help='Port for the proxy server')
    parser.add_argument('--browser-port', type=int, default=9222, help='Port for the Chromium browser')
    args = parser.parse_args()
    process(args.port, args.browser_port)
