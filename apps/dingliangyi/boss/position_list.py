import json
import time
from typing import Dict, List, Any, Optional

import requests
from loguru import logger
from resx.config import *
from resx.mysql_dao import MySQLDao
from resx.redis_types import Redis
from urllib3 import disable_warnings

from position import positions, hot_city, conditions

disable_warnings()
redis = Redis(**CFG_REDIS_GS, db=3)
dao = MySQLDao(**CFG_MYSQL_GS_TEST, db_tb_name='prism.boss_position_list', primary_index_fields=(['job_name', 'brand_name'], []))

REDIS_PROGRESS_KEY = "boss_crawl_progress"
REDIS_COOKIES_KEY = "boss_cookies"
REDIS_EMPTY_COMBINATIONS_KEY = "boss_empty_combinations"
MAX_COOKIE_USAGE = 3

headers = {
    "referer": "https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB",
    "user-agent": "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
url = "https://www.zhipin.com/wapi/zpgeek/search/joblist.json"

base_params = {
    "page": "1",
    "pageSize": "30",
    "query": "",
    "expectInfo": "",
    "multiSubway": "",
    "multiBusinessDistrict": "",
    "scene": "1",
}


def get_cookies_from_redis() -> Optional[Dict[str, Any]]:
    """从Redis获取cookies"""
    try:
        cookie_data = redis.lpop(REDIS_COOKIES_KEY)
        if cookie_data:
            return json.loads(cookie_data)
        else:
            logger.warning("Redis中没有可用的cookies")
            return None
    except Exception as e:
        logger.error(f"从Redis获取cookies失败: {e}")
        return None


def save_progress_to_redis(progress_data: Dict[str, Any]) -> None:
    """保存进度到Redis"""
    try:
        redis.set(REDIS_PROGRESS_KEY, json.dumps(progress_data, ensure_ascii=False))
        logger.info(f"进度已保存: {progress_data}")
    except Exception as e:
        logger.error(f"保存进度到Redis失败: {e}")


def get_progress_from_redis() -> Optional[Dict[str, Any]]:
    """从Redis获取进度"""
    try:
        progress_data = redis.get(REDIS_PROGRESS_KEY)
        if progress_data:
            return json.loads(progress_data)
        return None
    except Exception as e:
        logger.error(f"从Redis获取进度失败: {e}")
        return None


def generate_combination_cache_key(combination: Dict[str, Any]) -> str:
    """生成参数组合的缓存键"""
    # 创建一个排序后的参数字符串，确保相同参数组合生成相同的键
    sorted_params = sorted(combination.items())
    param_str = "&".join([f"{k}={v}" for k, v in sorted_params])

    # 使用哈希值作为缓存键，避免键过长
    import hashlib
    cache_key = hashlib.md5(param_str.encode('utf-8')).hexdigest()
    return cache_key


def is_combination_cached_as_empty(combination: Dict[str, Any]) -> bool:
    """检查参数组合是否已被缓存为无结果"""
    try:
        cache_key = generate_combination_cache_key(combination)
        return redis.sismember(REDIS_EMPTY_COMBINATIONS_KEY, cache_key)
    except Exception as e:
        logger.error(f"检查缓存失败: {e}")
        return False


def cache_empty_combination(combination: Dict[str, Any]) -> None:
    """将无结果的参数组合存储到Redis缓存"""
    try:
        cache_key = generate_combination_cache_key(combination)
        redis.sadd(REDIS_EMPTY_COMBINATIONS_KEY, cache_key)
        logger.debug(f"缓存无结果组合: {cache_key}")
    except Exception as e:
        logger.error(f"缓存组合失败: {e}")


def get_empty_combinations_count() -> int:
    """获取缓存中无结果组合的数量"""
    try:
        return redis.scard(REDIS_EMPTY_COMBINATIONS_KEY)
    except Exception as e:
        logger.error(f"获取缓存数量失败: {e}")
        return 0


def clear_empty_combinations_cache() -> bool:
    """清理无结果组合的缓存"""
    try:
        redis.delete(REDIS_EMPTY_COMBINATIONS_KEY)
        logger.info("已清理无结果组合缓存")
        return True
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        return False


def get_position_category(position_code: int) -> str:
    """获取职位类别"""
    # 技术开发类
    tech_dev_positions = [100101, 100102, 100103, 100109, 100106, 100107, 100116, 100114, 100121,
                          100124, 100125, 100123, 100199, 100901, 100202, 100203, 100209, 100211,
                          100210, 100212, 100208, 100213]

    # 高级技术类（算法、架构等）
    senior_tech_positions = [101306, 100117, 101310, 100104, 101311, 101312, 100118, 100115,
                             101305, 101309, 101313, 100120, 101307, 101301, 101302, 101308,
                             100704, 100702, 100705, 100707, 100706]

    # 测试运维类
    test_ops_positions = [100301, 100309, 100302, 100303, 100305, 100308, 100307, 100304, 100310,
                          100401, 100405, 100403, 100407, 100404, 100402, 100406, 100409, 100408, 100410]

    # 数据分析类
    data_positions = [100511, 100508, 100507, 100506, 100512, 100514, 100122, 100515]

    # 销售市场类
    sales_marketing_positions = [140301, 140310, 140314, 140307, 140304, 140317, 140305, 140316, 140302,
                                 140101, 140111, 140109, 140104, 130109, 140115, 130111, 130122, 130104]

    # 管理类
    management_positions = [100601, 100603, 150104, 150403, 150108, 150102, 150103, 150105, 150109]

    # 产品运营类
    product_ops_positions = [110101, 110108, 110302, 110106, 110110, 110105, 110103, 130101, 130102,
                             130103, 130106, 130108, 130110, 130118]

    if position_code in tech_dev_positions:
        return "tech_dev"
    elif position_code in senior_tech_positions:
        return "senior_tech"
    elif position_code in test_ops_positions:
        return "test_ops"
    elif position_code in data_positions:
        return "data"
    elif position_code in sales_marketing_positions:
        return "sales_marketing"
    elif position_code in management_positions:
        return "management"
    elif position_code in product_ops_positions:
        return "product_ops"
    else:
        return "other"


def validate_combination(combination: Dict[str, Any]) -> bool:
    """验证搜索组合的合理性"""
    position_code = combination['position']
    experience_code = combination['experience']
    salary_code = combination['salary']
    degree_code = combination['degree']
    job_type_code = combination['jobType']

    position_category = get_position_category(position_code)

    # 1. 薪资与经验的匹配性
    # 应届生(102)和在校生(108)不应该有高薪
    if experience_code in [102, 108] and salary_code in [406, 407]:  # 20K+薪资
        return False

    # 1年以内经验不应该有超高薪
    if experience_code == 103 and salary_code == 407:  # 50K+薪资
        return False

    # 10年以上经验不应该有低薪
    if experience_code == 107 and salary_code in [402, 403]:  # 5K以下薪资
        return False

    # 2. 学历与职位的匹配性
    # 高级技术职位通常需要本科以上学历
    if position_category == "senior_tech" and degree_code in [209, 208, 206]:  # 高中及以下
        return False

    # 算法、数据类职位通常需要本科以上学历
    if position_category == "data" and degree_code in [209, 208, 206]:
        return False

    # 管理类职位通常需要大专以上学历
    if position_category == "management" and degree_code in [209, 208]:  # 初中及以下、中专/中技
        return False

    # 3. 经验与学历的匹配性
    # 10年以上经验但只有高中及以下学历不合理
    if experience_code == 107 and degree_code in [209, 208, 206]:
        return False

    # 5年以上经验但只有初中学历不合理
    if experience_code in [106, 107] and degree_code == 209:
        return False

    # 4. 职位与工作类型的匹配性
    # 高级技术职位、管理职位不适合兼职
    if job_type_code == 1903 and position_category in ["senior_tech", "management"]:  # 兼职
        return False

    # 5. 薪资与学历的匹配性
    # 高薪职位与低学历不匹配
    if salary_code in [406, 407] and degree_code in [209, 208, 206]:  # 20K+薪资配高中及以下学历
        return False

    # 6. 避免过于严格的条件组合
    # 博士学历 + 应届生 + 高薪的组合虽然理论可能，但实际很少
    if degree_code == 205 and experience_code in [102, 108] and salary_code in [406, 407]:
        return False

    return True


def is_combination_likely_to_have_results(combination: Dict[str, Any]) -> bool:
    """判断组合是否可能有搜索结果，避免过于严格或宽松的条件"""
    position_code = combination['position']
    experience_code = combination['experience']
    salary_code = combination['salary']
    degree_code = combination['degree']
    city_code = combination['city']

    position_category = get_position_category(position_code)

    # 获取城市名称
    city_name = next((c['name'] for c in hot_city if c['code'] == city_code), '')

    # 1. 避免在小城市搜索高级技术职位
    small_cities = ['厦门', '长沙', '郑州', '佛山', '合肥', '济南', '青岛', '昆明', '南昌', '石家庄', '宁波', '福州']
    if position_category == "senior_tech" and city_name in small_cities:
        return False

    # 2. 避免过于严格的条件组合（高经验+高学历+高薪在小众职位上）
    if (experience_code in [106, 107] and  # 5年以上经验
            degree_code in [204, 205] and  # 硕士以上学历
            salary_code in [406, 407] and  # 20K以上薪资
            position_category in ["test_ops", "other"]):  # 相对小众的职位
        return False

    # 3. 避免过于宽松的条件组合（可能结果过多）
    if (experience_code in [102, 103] and  # 应届生或1年以内
            salary_code in [402, 403] and  # 5K以下薪资
            degree_code in [209, 208] and  # 初中或中专学历
            position_category == "sales_marketing"):  # 销售市场类职位
        return False

    # 4. 技术职位在非技术城市的合理性检查
    tech_cities = ['北京', '上海', '深圳', '杭州', '广州', '苏州', '武汉', '成都', '西安', '天津']
    if position_category in ["senior_tech", "tech_dev", "data"] and city_name not in tech_cities:
        # 技术职位在非技术城市，薪资不应过高
        if salary_code in [406, 407]:  # 20K以上薪资
            return False

    return True


def get_position_specific_params(position_code: int) -> Dict[str, List]:
    """根据职位类型获取适合的参数范围"""
    position_category = get_position_category(position_code)

    # 获取所有参数，排除code为0的选项
    all_experience = [exp for exp in conditions['experienceList'] if exp['code'] != 0]
    all_salary = [sal for sal in conditions['salaryList'] if sal['code'] != 0]
    all_degree = [deg for deg in conditions['degreeList'] if deg['code'] != 0]
    all_job_type = [jt for jt in conditions['jobTypeList'] if jt['code'] != 0]
    all_stage = [st for st in conditions['stageList'] if st['code'] != 0]
    all_scale = [sc for sc in conditions['scaleList'] if sc['code'] != 0]

    # 根据职位类型调整参数范围
    if position_category == "senior_tech":
        # 高级技术职位：更高经验要求，更高薪资，更高学历
        experience_list = [exp for exp in all_experience if exp['code'] in [104, 105, 106, 107]]  # 1年以上
        salary_list = [sal for sal in all_salary if sal['code'] in [404, 405, 406, 407]]  # 5K以上
        degree_list = [deg for deg in all_degree if deg['code'] in [202, 203, 204, 205]]  # 大专以上
    elif position_category == "tech_dev":
        # 普通技术职位：中等要求
        experience_list = all_experience[:6]  # 除了10年以上
        salary_list = all_salary[1:]  # 除了3K以下
        degree_list = all_degree[2:]  # 除了初中、中专
    elif position_category == "data":
        # 数据类职位：较高学历要求
        experience_list = all_experience[:6]
        salary_list = all_salary[2:]  # 5K以上
        degree_list = [deg for deg in all_degree if deg['code'] in [202, 203, 204, 205]]  # 大专以上
    elif position_category == "management":
        # 管理类职位：较高经验和学历要求
        experience_list = [exp for exp in all_experience if exp['code'] in [104, 105, 106, 107]]  # 1年以上
        salary_list = all_salary[2:]  # 5K以上
        degree_list = all_degree[3:]  # 高中以上
    elif position_category == "sales_marketing":
        # 销售市场类：相对宽松，但排除过低要求
        experience_list = all_experience
        salary_list = all_salary[1:]  # 除了3K以下
        degree_list = all_degree[2:]  # 除了初中、中专
    else:
        # 其他职位：使用默认范围
        experience_list = all_experience[:5]
        salary_list = all_salary[:5]
        degree_list = all_degree[:5]

    return {
        'experience': experience_list,
        'salary': salary_list,
        'degree': degree_list,
        'job_type': all_job_type,
        'stage': all_stage[:4],  # 限制融资阶段数量
        'scale': all_scale[:4]  # 限制公司规模数量
    }


def generate_search_combinations() -> List[Dict[str, Any]]:
    """生成所有搜索条件的排列组合"""
    combinations = []

    # 遍历所有职位，确保每个职位都被包含
    all_positions = positions  # 使用所有职位
    selected_cities = hot_city[:]  # 选择前10个热门城市，保证覆盖面

    logger.info(f"开始生成组合：{len(all_positions)} 个职位 × {len(selected_cities)} 个城市")

    # 以职位为主要维度进行组合
    for position in all_positions:
        position_category = get_position_category(position['code'])

        # 根据职位类型获取适合的参数
        position_params = get_position_specific_params(position['code'])

        # 为每个职位选择合适的城市子集（避免组合过多）
        if position_category in ["senior_tech", "data", "management"]:
            # 高级职位主要在一线城市
            city_subset = [city for city in selected_cities if city['name'] in ['北京', '上海', '深圳', '杭州', '广州']]
        else:
            # 其他职位可以在更多城市
            city_subset = selected_cities[:8]

        for city in city_subset:
            # 控制每个城市-职位组合的参数数量
            experience_subset = position_params['experience'][:3]  # 最多3个经验选项
            salary_subset = position_params['salary'][:3]  # 最多3个薪资选项
            degree_subset = position_params['degree'][:3]  # 最多3个学历选项

            for experience in experience_subset:
                for salary in salary_subset:
                    for degree in degree_subset:
                        for job_type in position_params['job_type']:
                            # 进一步控制stage和scale的组合
                            for stage in position_params['stage'][:2]:  # 最多2个融资阶段
                                for scale in position_params['scale'][:2]:  # 最多2个公司规模
                                    combination = {
                                        'position': position['code'],
                                        'city': city['code'],
                                        'experience': experience['code'],
                                        'salary': salary['code'],
                                        'degree': degree['code'],
                                        'jobType': job_type['code'],
                                        'stage': stage['code'],
                                        'scale': scale['code'],
                                    }

                                    # 验证组合的合理性和可能性
                                    if (validate_combination(combination) and
                                            is_combination_likely_to_have_results(combination)):
                                        combinations.append(combination)

    # 去重组合（避免重复的参数组合）
    unique_combinations = []
    seen_combinations = set()

    for combo in combinations:
        combo_key = tuple(sorted(combo.items()))
        if combo_key not in seen_combinations:
            seen_combinations.add(combo_key)
            unique_combinations.append(combo)

    # 进一步优化：确保每个职位都有合理的组合数量
    optimized_combinations = optimize_combinations_by_position(unique_combinations)

    logger.info(f"生成了 {len(combinations)} 个搜索组合，去重后 {len(unique_combinations)} 个，优化后 {len(optimized_combinations)} 个")
    return optimized_combinations


def optimize_combinations_by_position(combinations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """按职位优化组合数量，确保每个职位都有合理的覆盖"""
    position_combinations = {}

    # 按职位分组
    for combo in combinations:
        position_code = combo['position']
        if position_code not in position_combinations:
            position_combinations[position_code] = []
        position_combinations[position_code].append(combo)

    optimized = []

    for position_code, combos in position_combinations.items():
        position_category = get_position_category(position_code)

        # 根据职位类型确定保留的组合数量
        if position_category == "senior_tech":
            max_combos = 50  # 高级技术职位保留更多组合
        elif position_category in ["tech_dev", "data"]:
            max_combos = 40  # 技术和数据职位
        elif position_category == "management":
            max_combos = 30  # 管理职位
        elif position_category == "sales_marketing":
            max_combos = 35  # 销售市场职位
        else:
            max_combos = 25  # 其他职位

        # 如果组合数量超过限制，进行筛选
        if len(combos) > max_combos:
            # 优先保留一线城市的组合
            priority_cities = ['北京', '上海', '深圳', '杭州', '广州']
            priority_combos = []
            other_combos = []

            for combo in combos:
                city_name = next((c['name'] for c in hot_city if c['code'] == combo['city']), '')
                if city_name in priority_cities:
                    priority_combos.append(combo)
                else:
                    other_combos.append(combo)

            # 先取优先城市的组合，再补充其他城市的组合
            selected_combos = priority_combos[:max_combos // 2] + other_combos[:max_combos // 2]
            optimized.extend(selected_combos[:max_combos])
        else:
            optimized.extend(combos)

    return optimized


def fetch_job_list(cookies: Dict[str, str], IP: str, search_params: Dict[str, Any]) -> bool:
    """获取职位列表数据，返回是否成功"""
    max_retries = 3

    for retry in range(max_retries):
        try:
            params = base_params.copy()
            params.update(search_params)
            params["_"] = str(int(time.time() * 1000))

            session = requests.Session()
            session.proxies = {'http': IP, 'https': IP}

            response = session.get(url, headers=headers, verify=False, timeout=10, cookies=cookies, params=params)

            if response.status_code != 200:
                logger.warning(f"请求失败，状态码: {response.status_code}, 重试 {retry + 1}/{max_retries}")
                continue

            data = response.json()

            if 'zpData' not in data or 'jobList' not in data['zpData']:
                logger.warning(f"响应数据格式异常: {data}, 重试 {retry + 1}/{max_retries}")
                continue

            job_list = data['zpData']['jobList']

            if not job_list:
                logger.info("当前搜索条件下没有职位数据，将组合缓存为无结果")
                # 将无结果的组合缓存起来
                cache_empty_combination(search_params)
                return True

            # 保存数据
            for info in job_list:
                try:
                    job_data = {
                        'job_name': info.get('jobName', ''),
                        'brand_name': info.get('brandName', ''),
                        'salary_desc': info.get('salaryDesc', ''),
                        'job_experience': info.get('jobExperience', ''),
                        'job_degree': info.get('jobDegree', ''),
                        'city_name': info.get('cityName', ''),
                        'skills': ', '.join(info.get('skills', [])),
                        'brand_industry': info.get('brandIndustry', ''),
                        'brand_scale_name': info.get('brandScaleName', ''),
                        'area_district': info.get('areaDistrict', ''),
                        'business_district': info.get('businessDistrict', ''),
                        'industry': info.get('industry', ''),
                        'security_id': info.get('securityId', ''),
                        'lid': info.get('lid', ''),
                    }
                    dao.save(job_data)
                    logger.debug(f"保存职位数据: {job_data['job_name']} - {job_data['brand_name']}")
                except Exception as e:
                    logger.error(f"保存单条数据失败: {e}")

            logger.info(f"成功获取并保存 {len(job_list)} 条职位数据")
            return True

        except Exception as e:
            logger.error(f"请求失败 (重试 {retry + 1}/{max_retries}): {e}")
            if retry < max_retries - 1:
                time.sleep(2)  # 重试前等待

    return False


def print_combination_stats(combinations: List[Dict[str, Any]]) -> None:
    """打印组合统计信息"""
    if not combinations:
        return

    # 统计城市分布
    city_stats = {}
    position_stats = {}

    for combo in combinations:
        city_code = combo['city']
        position_code = combo['position']

        city_name = next((c['name'] for c in hot_city if c['code'] == city_code), f"城市{city_code}")
        position_name = next((p['name'] for p in positions if p['code'] == position_code), f"职位{position_code}")

        city_stats[city_name] = city_stats.get(city_name, 0) + 1
        position_stats[position_name] = position_stats.get(position_name, 0) + 1

    logger.info("=== 搜索组合统计 ===")
    logger.info(f"总组合数: {len(combinations)}")
    logger.info(f"涉及城市: {list(city_stats.keys())}")
    logger.info(f"涉及职位: {list(position_stats.keys())}")


def main():
    """主函数：循环遍历参数并抓取数据"""
    logger.info("开始Boss职位数据抓取任务")

    # 生成所有搜索组合
    all_combinations = generate_search_combinations()

    # 打印统计信息
    print_combination_stats(all_combinations)

    # 获取上次的进度
    progress = get_progress_from_redis()
    start_index = 0

    if progress:
        start_index = progress.get('current_index', 0)
        logger.info(f"从上次进度继续: 第 {start_index} 个组合")
    else:
        logger.info("开始新的抓取任务")

    current_cookie_data = None
    cookie_usage_count = 0

    # 缓存统计
    skipped_count = 0
    cached_empty_count = get_empty_combinations_count()
    logger.info(f"当前缓存中有 {cached_empty_count} 个无结果组合")

    for i in range(start_index, len(all_combinations)):
        combination = all_combinations[i]

        # 检查是否需要获取新的cookies
        if current_cookie_data is None or cookie_usage_count >= MAX_COOKIE_USAGE:
            current_cookie_data = get_cookies_from_redis()
            if current_cookie_data is None:
                logger.error("无法获取cookies，等待30秒后重试")
                time.sleep(30)
                continue
            cookie_usage_count = 0
            logger.info(f"获取新的cookies，IP: {current_cookie_data['IP']}")

        # 保存当前进度
        progress_data = {
            'current_index': i,
            'total_combinations': len(all_combinations),
            'current_combination': combination,
            'timestamp': time.time()
        }
        save_progress_to_redis(progress_data)

        # 获取组合的可读名称用于日志
        position_name = next((p['name'] for p in positions if p['code'] == combination['position']), '未知职位')
        city_name = next((c['name'] for c in hot_city if c['code'] == combination['city']), '未知城市')

        logger.info(f"处理第 {i + 1}/{len(all_combinations)} 个组合: {city_name} - {position_name} (参数: {combination})")

        # 检查组合是否已被缓存为无结果
        if is_combination_cached_as_empty(combination):
            skipped_count += 1
            logger.info(f"跳过已缓存的无结果组合 (第 {i + 1} 个)，累计跳过: {skipped_count}")
            continue

        # 尝试获取数据
        success = fetch_job_list(
            current_cookie_data['cookies'],
            current_cookie_data['IP'],
            combination
        )

        if success:
            cookie_usage_count += 1
            logger.info(f"成功完成第 {i + 1} 个组合，cookies使用次数: {cookie_usage_count}")
        else:
            logger.error(f"第 {i + 1} 个组合失败，将获取新的cookies")
            current_cookie_data = None
            cookie_usage_count = MAX_COOKIE_USAGE  # 强制获取新cookies

        # 请求间隔
        time.sleep(2)

    logger.info("所有搜索组合处理完成")

    # 显示缓存统计信息
    final_cached_count = get_empty_combinations_count()
    new_cached_count = final_cached_count - cached_empty_count
    logger.info(f"=== 缓存统计 ===")
    logger.info(f"本次跳过的组合数: {skipped_count}")
    logger.info(f"本次新增缓存的无结果组合: {new_cached_count}")
    logger.info(f"缓存中总的无结果组合数: {final_cached_count}")

    # 清除进度记录
    redis.delete(REDIS_PROGRESS_KEY)


if __name__ == '__main__':
    main()
