import time

import requests
from loguru import logger
from resx.config import *
from resx.mysql_dao import MySQLDao
from resx.redis_types import Redis
from urllib3 import disable_warnings

from position import positions, hot_city, all_city, conditions, industrys

disable_warnings()
redis = Redis(**CFG_REDIS_GS, db=3)
dao = MySQLDao(**CFG_MYSQL_GS_TEST, db_tb_name='prism.boss_position_list', primary_index_fields=(['job_name', 'brand_name'], []))
headers = {
    "referer": "https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB",
    # "traceid": "F-a2cd6cbKYRvIHu2r",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
url = "https://www.zhipin.com/wapi/zpgeek/search/joblist.json"
params = {
    "page": "1",
    "pageSize": "30",

    "position": "100101",
    "city": "101010100",

    "industry": "",
    "query": "",

    # conditions
    "jobType": "",
    "salary": "",
    "experience": "",
    "stage": "",
    "scale": "",
    "degree": "",

    "expectInfo": "",
    "multiSubway": "",
    "multiBusinessDistrict": "",

    "scene": "1",
    "_": str(int(time.time() * 1000)),
}


def fetch_job_list(cookies, IP, position, city, industry, job_type, salary, experience, stage, scale, degree):
    for _ in range(2):
        try:
            params.update({
                'position': position,
                'city': city,
                'industry': industry,
                'jobType': job_type,
                'salary': salary,
                'experience': experience,
                'stage': stage,
                'scale': scale,
                'degree': degree
            })
            session = requests.Session()
            session.proxies = {'http': IP, 'https': IP}
            response = session.get(url, headers=headers, verify=False, timeout=5, cookies=cookies, params=params)
            infos = response.json()['zpData']['jobList']
            for info in infos:
                data = {
                    'job_name': info['jobName'],
                    'brand_name': info['brandName'],
                    'salary_desc': info['salaryDesc'],
                    'job_experience': info['jobExperience'],
                    'job_degree': info['jobDegree'],
                    'city_name': info['cityName'],
                    'skills': ', '.join(info['skills']),
                    'brand_industry': info['brandIndustry'],
                    'brand_scale_name': info['brandScaleName'],
                    'area_district': info['areaDistrict'],
                    'business_district': info['businessDistrict'],
                    'industry': info['industry'],
                    'security_id': info['securityId'],
                    'lid': info['lid'],
                }
                logger.info(data)
                dao.save(data)
        except Exception as e:
            logger.error(f"Error fetching job list: {e}")
            break


if __name__ == '__main__':
    pass
