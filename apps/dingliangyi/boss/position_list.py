import json
import time
from typing import Dict, List, Any, Optional

import requests
from loguru import logger
from resx.config import *
from resx.mysql_dao import MySQLDao
from resx.redis_types import Redis
from urllib3 import disable_warnings

from position import positions, hot_city, conditions

disable_warnings()
redis = Redis(**CFG_REDIS_GS, db=3)
dao = MySQLDao(**CFG_MYSQL_GS_TEST, db_tb_name='prism.boss_position_list', primary_index_fields=(['job_name', 'brand_name'], []))

REDIS_PROGRESS_KEY = "boss_crawl_progress"
REDIS_COOKIES_KEY = "boss_cookies"
MAX_COOKIE_USAGE = 3

headers = {
    "referer": "https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Safari/537.36",
}
url = "https://www.zhipin.com/wapi/zpgeek/search/joblist.json"

base_params = {
    "page": "1",
    "pageSize": "30",
    "query": "",
    'industry': "",
    "expectInfo": "",
    "multiSubway": "",
    "multiBusinessDistrict": "",
    "scene": "1",
}


def get_cookies_from_redis() -> Optional[Dict[str, Any]]:
    """从Redis获取cookies"""
    try:
        cookie_data = redis.lpop(REDIS_COOKIES_KEY)
        if cookie_data:
            return json.loads(cookie_data)
        else:
            logger.warning("Redis中没有可用的cookies")
            return None
    except Exception as e:
        logger.error(f"从Redis获取cookies失败: {e}")
        return None


def save_progress_to_redis(progress_data: Dict[str, Any]) -> None:
    """保存进度到Redis"""
    try:
        redis.set(REDIS_PROGRESS_KEY, json.dumps(progress_data, ensure_ascii=False))
        logger.info(f"进度已保存: {progress_data}")
    except Exception as e:
        logger.error(f"保存进度到Redis失败: {e}")


def get_progress_from_redis() -> Optional[Dict[str, Any]]:
    """从Redis获取进度"""
    try:
        progress_data = redis.get(REDIS_PROGRESS_KEY)
        if progress_data:
            return json.loads(progress_data)
        return None
    except Exception as e:
        logger.error(f"从Redis获取进度失败: {e}")
        return None


def validate_combination(combination: Dict[str, Any]) -> bool:
    """验证搜索组合的合理性"""
    # 检查高薪职位与学历的匹配
    high_salary_codes = [406, 407]  # 20K以上的薪资
    low_degree_codes = [209, 208, 206]  # 初中及以下、中专/中技、高中

    if combination['salary'] in high_salary_codes and combination['degree'] in low_degree_codes:
        return False

    # 检查经验与薪资的匹配
    high_experience_codes = [106, 107]  # 5年以上经验
    low_salary_codes = [402, 403]  # 5K以下薪资

    if combination['experience'] in high_experience_codes and combination['salary'] in low_salary_codes:
        return False

    return True


def generate_search_combinations() -> List[Dict[str, Any]]:
    """生成所有搜索条件的排列组合"""
    combinations = []

    # 选择部分城市和职位进行组合，避免组合过多
    selected_positions = positions[:]  # 取前20个职位
    selected_cities = hot_city[:]  # 取前8个热门城市

    # 获取条件参数，排除code为0的选项
    experience_list = [exp for exp in conditions['experienceList'] if exp['code'] != 0][:4]
    salary_list = [sal for sal in conditions['salaryList'] if sal['code'] != 0][:4]
    degree_list = [deg for deg in conditions['degreeList'] if deg['code'] != 0][:4]
    job_type_list = [jt for jt in conditions['jobTypeList'] if jt['code'] != 0]
    stage_list = [st for st in conditions['stageList'] if st['code'] != 0][:4]
    scale_list = [sc for sc in conditions['scaleList'] if sc['code'] != 0][:4]

    # 以城市和职位为主组合条件
    for city in selected_cities:
        for position in selected_positions:
            # 为了控制组合数量，每个城市-职位组合只选择部分其他条件
            for experience in experience_list[:2]:  # 只取前2个经验要求
                for salary in salary_list[:2]:  # 只取前2个薪资要求
                    for degree in degree_list[:2]:  # 只取前2个学历要求
                        for job_type in job_type_list:  # 全部工作类型
                            for stage in stage_list[:2]:  # 只取前2个融资阶段
                                for scale in scale_list[:2]:  # 只取前2个公司规模
                                    combination = {
                                        'position': position['code'],
                                        'city': city['code'],
                                        'experience': experience['code'],
                                        'salary': salary['code'],
                                        'degree': degree['code'],
                                        'jobType': job_type['code'],
                                        'stage': stage['code'],
                                        'scale': scale['code'],
                                    }
                                    if validate_combination(combination):
                                        combinations.append(combination)

    # 去重组合（避免重复的参数组合）
    unique_combinations = []
    seen_combinations = set()

    for combo in combinations:
        combo_key = tuple(sorted(combo.items()))
        if combo_key not in seen_combinations:
            seen_combinations.add(combo_key)
            unique_combinations.append(combo)

    logger.info(f"生成了 {len(combinations)} 个搜索组合，去重后 {len(unique_combinations)} 个")
    return unique_combinations


def fetch_job_list(cookies: Dict[str, str], IP: str, search_params: Dict[str, Any]) -> bool:
    """获取职位列表数据，返回是否成功"""
    max_retries = 3

    for retry in range(max_retries):
        try:
            params = base_params.copy()
            params.update(search_params)
            params["_"] = str(int(time.time() * 1000))

            session = requests.Session()
            session.proxies = {'http': IP, 'https': IP}

            response = session.get(url, headers=headers, verify=False, timeout=10, cookies=cookies, params=params)

            if response.status_code != 200:
                logger.warning(f"请求失败，状态码: {response.status_code}, 重试 {retry + 1}/{max_retries}")
                continue

            data = response.json()

            if 'zpData' not in data or 'jobList' not in data['zpData']:
                logger.warning(f"响应数据格式异常: {data}, 重试 {retry + 1}/{max_retries}")
                continue

            job_list = data['zpData']['jobList']

            if not job_list:
                logger.info("当前搜索条件下没有职位数据")
                return True

            # 保存数据
            for info in job_list:
                try:
                    job_data = {
                        'job_name': info.get('jobName', ''),
                        'brand_name': info.get('brandName', ''),
                        'salary_desc': info.get('salaryDesc', ''),
                        'job_experience': info.get('jobExperience', ''),
                        'job_degree': info.get('jobDegree', ''),
                        'city_name': info.get('cityName', ''),
                        'skills': ', '.join(info.get('skills', [])),
                        'brand_industry': info.get('brandIndustry', ''),
                        'brand_scale_name': info.get('brandScaleName', ''),
                        'area_district': info.get('areaDistrict', ''),
                        'business_district': info.get('businessDistrict', ''),
                        'industry': info.get('industry', ''),
                        'security_id': info.get('securityId', ''),
                        'lid': info.get('lid', ''),
                    }
                    dao.save(job_data)
                    logger.debug(f"保存职位数据: {job_data['job_name']} - {job_data['brand_name']}")
                except Exception as e:
                    logger.error(f"保存单条数据失败: {e}")

            logger.info(f"成功获取并保存 {len(job_list)} 条职位数据")
            return True

        except Exception as e:
            logger.error(f"请求失败 (重试 {retry + 1}/{max_retries}): {e}")
            if retry < max_retries - 1:
                time.sleep(2)  # 重试前等待

    return False


def print_combination_stats(combinations: List[Dict[str, Any]]) -> None:
    """打印组合统计信息"""
    if not combinations:
        return

    # 统计城市分布
    city_stats = {}
    position_stats = {}

    for combo in combinations:
        city_code = combo['city']
        position_code = combo['position']

        city_name = next((c['name'] for c in hot_city if c['code'] == city_code), f"城市{city_code}")
        position_name = next((p['name'] for p in positions if p['code'] == position_code), f"职位{position_code}")

        city_stats[city_name] = city_stats.get(city_name, 0) + 1
        position_stats[position_name] = position_stats.get(position_name, 0) + 1

    logger.info("=== 搜索组合统计 ===")
    logger.info(f"总组合数: {len(combinations)}")
    logger.info(f"涉及城市: {list(city_stats.keys())}")
    logger.info(f"涉及职位: {list(position_stats.keys())}")


def main():
    """主函数：循环遍历参数并抓取数据"""
    logger.info("开始Boss职位数据抓取任务")

    # 生成所有搜索组合
    all_combinations = generate_search_combinations()

    # 打印统计信息
    print_combination_stats(all_combinations)

    # 获取上次的进度
    progress = get_progress_from_redis()
    start_index = 0

    if progress:
        start_index = progress.get('current_index', 0)
        logger.info(f"从上次进度继续: 第 {start_index} 个组合")
    else:
        logger.info("开始新的抓取任务")

    current_cookie_data = None
    cookie_usage_count = 0

    for i in range(start_index, len(all_combinations)):
        combination = all_combinations[i]

        # 检查是否需要获取新的cookies
        if current_cookie_data is None or cookie_usage_count >= MAX_COOKIE_USAGE:
            current_cookie_data = get_cookies_from_redis()
            if current_cookie_data is None:
                logger.error("无法获取cookies，等待30秒后重试")
                time.sleep(30)
                continue
            cookie_usage_count = 0
            logger.info(f"获取新的cookies，IP: {current_cookie_data['IP']}")

        # 保存当前进度
        progress_data = {
            'current_index': i,
            'total_combinations': len(all_combinations),
            'current_combination': combination,
            'timestamp': time.time()
        }
        save_progress_to_redis(progress_data)

        # 获取组合的可读名称用于日志
        position_name = next((p['name'] for p in positions if p['code'] == combination['position']), '未知职位')
        city_name = next((c['name'] for c in hot_city if c['code'] == combination['city']), '未知城市')

        logger.info(f"处理第 {i + 1}/{len(all_combinations)} 个组合: {city_name} - {position_name} (参数: {combination})")

        # 尝试获取数据
        success = fetch_job_list(
            current_cookie_data['cookies'],
            current_cookie_data['IP'],
            combination
        )

        if success:
            cookie_usage_count += 1
            logger.info(f"成功完成第 {i + 1} 个组合，cookies使用次数: {cookie_usage_count}")
        else:
            logger.error(f"第 {i + 1} 个组合失败，将获取新的cookies")
            current_cookie_data = None
            cookie_usage_count = MAX_COOKIE_USAGE  # 强制获取新cookies

        # 请求间隔
        time.sleep(2)

    logger.info("所有搜索组合处理完成")
    # 清除进度记录
    redis.delete(REDIS_PROGRESS_KEY)


if __name__ == '__main__':
    main()
