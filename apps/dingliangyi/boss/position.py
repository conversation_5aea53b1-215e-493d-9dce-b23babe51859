positions = [
    {
        "code": 1010000,
        "name": "互联网/AI",
        "subLevelModelList": [
            {
                "code": 1000020,
                "name": "后端开发",
                "subLevelModelList": [
                    {
                        "code": 100101,
                        "name": "Java"
                    },
                    {
                        "code": 100102,
                        "name": "C/C++",
                    },
                    {
                        "code": 100103,
                        "name": "PHP",
                    },
                    {
                        "code": 100109,
                        "name": "Python",
                    },
                    {
                        "code": 100106,
                        "name": "C#",
                    },
                    {
                        "code": 100107,
                        "name": ".NET",
                    },
                    {
                        "code": 100116,
                        "name": "Golang",
                    },
                    {
                        "code": 100114,
                        "name": "Node.js",
                    },
                    {
                        "code": 100121,
                        "name": "语音/视频/图形开发",
                    },
                    {
                        "code": 101313,
                        "name": "高性能计算工程师",
                    },
                    {
                        "code": 100124,
                        "name": "GIS工程师",
                    },
                    {
                        "code": 100125,
                        "name": "区块链工程师",
                    },
                    {
                        "code": 100123,
                        "name": "全栈工程师",
                    },
                    {
                        "code": 100608,
                        "name": "软件项目经理",
                    },
                    {
                        "code": 100199,
                        "name": "其他后端开发",
                    }
                ],
            },
            {
                "code": 1000030,
                "name": "前端/移动开发",
                "subLevelModelList": [
                    {
                        "code": 100901,
                        "name": "前端开发工程师",
                    },
                    {
                        "code": 100202,
                        "name": "Android",
                    },
                    {
                        "code": 100203,
                        "name": "iOS",
                    },
                    {
                        "code": 100209,
                        "name": "U3D",
                    },
                    {
                        "code": 100211,
                        "name": "UE4",
                    },
                    {
                        "code": 100210,
                        "name": "Cocos",
                    },
                    {
                        "code": 100212,
                        "name": "技术美术",
                    },
                    {
                        "code": 100208,
                        "name": "JavaScript",
                    },
                    {
                        "code": 100213,
                        "name": "鸿蒙开发工程师",
                    }
                ],
            },
            {
                "code": 1000040,
                "name": "测试",
                "subLevelModelList": [
                    {
                        "code": 100301,
                        "name": "测试工程师",
                    },
                    {
                        "code": 100309,
                        "name": "软件测试",
                    },
                    {
                        "code": 100302,
                        "name": "自动化测试",
                    },
                    {
                        "code": 100303,
                        "name": "功能测试",
                    },
                    {
                        "code": 100305,
                        "name": "测试开发",
                    },
                    {
                        "code": 100308,
                        "name": "硬件测试",
                    },
                    {
                        "code": 100307,
                        "name": "游戏测试",
                    },
                    {
                        "code": 100304,
                        "name": "性能测试",
                    },
                    {
                        "code": 100310,
                        "name": "渗透测试",
                    },
                    {
                        "code": 100703,
                        "name": "测试经理",
                    }
                ],
            },
            {
                "code": 1000050,
                "name": "运维/技术支持",
                "subLevelModelList": [
                    {
                        "code": 100401,
                        "name": "运维工程师",
                    },
                    {
                        "code": 100405,
                        "name": "IT技术支持",
                    },
                    {
                        "code": 100403,
                        "name": "网络工程师",
                    },
                    {
                        "code": 100407,
                        "name": "网络安全",
                    },
                    {
                        "code": 100404,
                        "name": "系统工程师",
                    },
                    {
                        "code": 100402,
                        "name": "运维开发工程师",
                    },
                    {
                        "code": 100406,
                        "name": "系统管理员",
                    },
                    {
                        "code": 100409,
                        "name": "DBA",
                    },
                    {
                        "code": 290166,
                        "name": "电脑/打印机维修",
                    },
                    {
                        "code": 100408,
                        "name": "系统安全",
                    },
                    {
                        "code": 100410,
                        "name": "技术文档工程师",
                    }
                ],
            },
            {
                "code": 1000130,
                "name": "人工智能",
                "subLevelModelList": [
                    {
                        "code": 101306,
                        "name": "图像算法",
                    },
                    {
                        "code": 100117,
                        "name": "自然语言处理算法",
                    },
                    {
                        "code": 101310,
                        "name": "大模型算法",
                    },
                    {
                        "code": 100104,
                        "name": "数据挖掘",
                    },
                    {
                        "code": 101311,
                        "name": "规控算法",
                    },
                    {
                        "code": 101312,
                        "name": "SLAM算法",
                    },
                    {
                        "code": 100118,
                        "name": "推荐算法",
                    },
                    {
                        "code": 100115,
                        "name": "搜索算法",
                    },
                    {
                        "code": 101305,
                        "name": "语音算法",
                    },
                    {
                        "code": 101309,
                        "name": "风控算法",
                    },
                    {
                        "code": 101313,
                        "name": "高性能计算工程师",
                    },
                    {
                        "code": 100120,
                        "name": "算法工程师",
                    },
                    {
                        "code": 101307,
                        "name": "算法研究员",
                    },
                    {
                        "code": 101301,
                        "name": "机器学习",
                    },
                    {
                        "code": 101302,
                        "name": "深度学习",
                    },
                    {
                        "code": 101308,
                        "name": "自动驾驶系统工程师",
                    },
                    {
                        "code": 130121,
                        "name": "数据标注/AI训练师",
                    }
                ],
            },
            {
                "code": 1000140,
                "name": "销售技术支持",
                "subLevelModelList": [
                    {
                        "code": 101201,
                        "name": "售前技术支持",
                    },
                    {
                        "code": 101202,
                        "name": "售后技术支持",
                    },
                    {
                        "code": 101299,
                        "name": "销售技术支持",
                    },
                    {
                        "code": 160303,
                        "name": "客户成功",
                    }
                ],
            },
            {
                "code": 1000060,
                "name": "数据",
                "subLevelModelList": [
                    {
                        "code": 100511,
                        "name": "数据分析师",
                    },
                    {
                        "code": 100508,
                        "name": "数据开发",
                    },
                    {
                        "code": 100507,
                        "name": "数据仓库",
                    },
                    {
                        "code": 100506,
                        "name": "ETL工程师",
                    },
                    {
                        "code": 100104,
                        "name": "数据挖掘",
                    },
                    {
                        "code": 100512,
                        "name": "数据架构师",
                    },
                    {
                        "code": 100514,
                        "name": "爬虫工程师",
                    },
                    {
                        "code": 100122,
                        "name": "数据采集",
                    },
                    {
                        "code": 100515,
                        "name": "数据治理",
                    }
                ],
            },
            {
                "code": 1000070,
                "name": "技术项目管理",
                "subLevelModelList": [
                    {
                        "code": 100601,
                        "name": "项目经理/主管",
                    },
                    {
                        "code": 100608,
                        "name": "软件项目经理",
                    },
                    {
                        "code": 100606,
                        "name": "实施工程师",
                    },
                    {
                        "code": 100605,
                        "name": "实施顾问",
                    },
                    {
                        "code": 100607,
                        "name": "需求分析工程师",
                    },
                    {
                        "code": 100603,
                        "name": "项目专员/助理",
                    }
                ],
            },
            {
                "code": 1000120,
                "name": "高端技术职位",
                "subLevelModelList": [
                    {
                        "code": 100701,
                        "name": "技术经理",
                    },
                    {
                        "code": 100704,
                        "name": "架构师",
                    },
                    {
                        "code": 100702,
                        "name": "技术总监",
                    },
                    {
                        "code": 100705,
                        "name": "CTO/CIO",
                    },
                    {
                        "code": 100707,
                        "name": "技术合伙人",
                    },
                    {
                        "code": 100706,
                        "name": "运维总监",
                    }
                ],
            },
            {
                "code": 1000150,
                "name": "其他技术职位",
                "subLevelModelList": [
                    {
                        "code": 101101,
                        "name": "其他技术职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1210000,
        "name": "电子/电气/通信",
        "subLevelModelList": [
            {
                "code": 1000080,
                "name": "电子/硬件开发",
                "subLevelModelList": [
                    {
                        "code": 101401,
                        "name": "电子工程师",
                    },
                    {
                        "code": 100801,
                        "name": "硬件工程师",
                    },
                    {
                        "code": 100802,
                        "name": "嵌入式软件工程师",
                    },
                    {
                        "code": 100808,
                        "name": "FPGA开发",
                    },
                    {
                        "code": 100804,
                        "name": "单片机",
                    },
                    {
                        "code": 100806,
                        "name": "驱动开发工程师",
                    },
                    {
                        "code": 100811,
                        "name": "PCB工程师",
                    },
                    {
                        "code": 101408,
                        "name": "电子维修技术员",
                    },
                    {
                        "code": 100816,
                        "name": "射频工程师",
                    },
                    {
                        "code": 100805,
                        "name": "电路设计",
                    },
                    {
                        "code": 100807,
                        "name": "系统集成",
                    },
                    {
                        "code": 100818,
                        "name": "光学工程师",
                    },
                    {
                        "code": 100809,
                        "name": "DSP开发",
                    },
                    {
                        "code": 100819,
                        "name": "电源工程师",
                    },
                    {
                        "code": 300801,
                        "name": "电池工程师",
                    },
                    {
                        "code": 100820,
                        "name": "电声工程师",
                    }
                ],
            },
            {
                "code": 1000110,
                "name": "半导体/芯片",
                "subLevelModelList": [
                    {
                        "code": 101405,
                        "name": "集成电路IC设计",
                    },
                    {
                        "code": 101406,
                        "name": "数字IC验证工程师",
                    },
                    {
                        "code": 101407,
                        "name": "模拟版图设计工程师",
                    },
                    {
                        "code": 101409,
                        "name": "芯片测试工程师",
                    },
                    {
                        "code": 101410,
                        "name": "DFT工程师",
                    },
                    {
                        "code": 101403,
                        "name": "FAE",
                    },
                    {
                        "code": 101411,
                        "name": "数字前端设计师",
                    },
                    {
                        "code": 101412,
                        "name": "数字后端工程师",
                    },
                    {
                        "code": 101413,
                        "name": "模拟IC设计工程师",
                    }
                ],
            },
            {
                "code": 2600842,
                "name": "电气/自动化",
                "subLevelModelList": [
                    {
                        "code": 101402,
                        "name": "电气工程师",
                    },
                    {
                        "code": 101404,
                        "name": "电气设计工程师",
                    },
                    {
                        "code": 100803,
                        "name": "自动化工程师",
                    },
                    {
                        "code": 300310,
                        "name": "机电工程师",
                    },
                    {
                        "code": 220223,
                        "name": "建筑机电工程师",
                    },
                    {
                        "code": 101414,
                        "name": "电力工程师",
                    }
                ],
            },
            {
                "code": 2600422,
                "name": "技术项目管理",
                "subLevelModelList": [
                    {
                        "code": 100601,
                        "name": "项目经理/主管",
                    },
                    {
                        "code": 100606,
                        "name": "实施工程师",
                    },
                    {
                        "code": 100605,
                        "name": "实施顾问",
                    },
                    {
                        "code": 100817,
                        "name": "硬件项目经理",
                    },
                    {
                        "code": 100603,
                        "name": "项目专员/助理",
                    }
                ],
            },
            {
                "code": 1000100,
                "name": "通信",
                "subLevelModelList": [
                    {
                        "code": 101011,
                        "name": "通信项目专员",
                    },
                    {
                        "code": 101012,
                        "name": "通信项目经理",
                    },
                    {
                        "code": 101001,
                        "name": "通信技术工程师",
                    },
                    {
                        "code": 101002,
                        "name": "通信研发工程师",
                    },
                    {
                        "code": 101003,
                        "name": "数据通信工程师",
                    },
                    {
                        "code": 101018,
                        "name": "光网络工程师",
                    },
                    {
                        "code": 101007,
                        "name": "有线传输工程师",
                    },
                    {
                        "code": 101013,
                        "name": "核心网工程师",
                    },
                    {
                        "code": 101010,
                        "name": "通信标准化工程师",
                    },
                    {
                        "code": 100403,
                        "name": "网络工程师",
                    },
                    {
                        "code": 101005,
                        "name": "宽带装维",
                    },
                    {
                        "code": 101008,
                        "name": "无线/天线工程师",
                    }
                ],
            },
            {
                "code": 2600862,
                "name": "销售技术支持",
                "subLevelModelList": [
                    {
                        "code": 101201,
                        "name": "售前技术支持",
                    },
                    {
                        "code": 101202,
                        "name": "售后技术支持",
                    },
                    {
                        "code": 101299,
                        "name": "销售技术支持",
                    }
                ],
            },
            {
                "code": 2600852,
                "name": "运维/技术支持",
                "subLevelModelList": [
                    {
                        "code": 100401,
                        "name": "运维工程师",
                    },
                    {
                        "code": 100404,
                        "name": "系统工程师",
                    },
                    {
                        "code": 100410,
                        "name": "技术文档工程师",
                    }
                ],
            }
        ],
    },
    {
        "code": 1020000,
        "name": "产品",
        "subLevelModelList": [
            {
                "code": 1000160,
                "name": "产品经理",
                "subLevelModelList": [
                    {
                        "code": 110101,
                        "name": "产品经理",
                    },
                    {
                        "code": 110108,
                        "name": "产品专员/助理",
                    },
                    {
                        "code": 110302,
                        "name": "高级产品管理岗",
                    },
                    {
                        "code": 120302,
                        "name": "用户研究",
                    },
                    {
                        "code": 110106,
                        "name": "电商产品经理",
                    },
                    {
                        "code": 110110,
                        "name": "AI产品经理",
                    },
                    {
                        "code": 110105,
                        "name": "数据产品经理",
                    },
                    {
                        "code": 110103,
                        "name": "移动产品经理",
                    },
                    {
                        "code": 180501,
                        "name": "金融产品经理",
                    },
                    {
                        "code": 130133,
                        "name": "跨境电商产品开发",
                    },
                    {
                        "code": 110111,
                        "name": "化妆品产品经理",
                    },
                    {
                        "code": 110109,
                        "name": "硬件产品经理",
                    },
                    {
                        "code": 110401,
                        "name": "其他产品职位",
                    },
                    {
                        "code": 100607,
                        "name": "需求分析工程师",
                    }
                ],
            },
            {
                "code": 2600432,
                "name": "游戏策划/制作",
                "subLevelModelList": [
                    {
                        "code": 110107,
                        "name": "游戏策划",
                    },
                    {
                        "code": 120305,
                        "name": "系统策划",
                    },
                    {
                        "code": 120303,
                        "name": "游戏数值策划",
                    },
                    {
                        "code": 110303,
                        "name": "游戏制作人",
                    }
                ],
            }
        ],
    },
    {
        "code": 1040000,
        "name": "客服/运营",
        "subLevelModelList": [
            {
                "code": 1000270,
                "name": "客服",
                "subLevelModelList": [
                    {
                        "code": 130305,
                        "name": "客服专员",
                    },
                    {
                        "code": 130306,
                        "name": "客服主管",
                    },
                    {
                        "code": 130304,
                        "name": "客服经理",
                    },
                    {
                        "code": 130303,
                        "name": "网络客服",
                    },
                    {
                        "code": 130308,
                        "name": "电话客服",
                    },
                    {
                        "code": 130302,
                        "name": "售后客服",
                    },
                    {
                        "code": 130301,
                        "name": "售前客服",
                    }
                ],
            },
            {
                "code": 1001290,
                "name": "内容运营",
                "subLevelModelList": [
                    {
                        "code": 130111,
                        "name": "新媒体运营",
                    },
                    {
                        "code": 130122,
                        "name": "直播运营",
                    },
                    {
                        "code": 170108,
                        "name": "视频运营",
                    },
                    {
                        "code": 130104,
                        "name": "内容运营",
                    },
                    {
                        "code": 130113,
                        "name": "微信运营",
                    }
                ],
            },
            {
                "code": 1001300,
                "name": "电商运营",
                "subLevelModelList": [
                    {
                        "code": 130117,
                        "name": "国内电商运营",
                    },
                    {
                        "code": 130124,
                        "name": "跨境电商运营",
                    },
                    {
                        "code": 130107,
                        "name": "品类运营",
                    },
                    {
                        "code": 130126,
                        "name": "淘宝运营",
                    },
                    {
                        "code": 130127,
                        "name": "天猫运营",
                    },
                    {
                        "code": 130128,
                        "name": "京东运营",
                    },
                    {
                        "code": 130129,
                        "name": "拼多多运营",
                    },
                    {
                        "code": 130130,
                        "name": "亚马逊运营",
                    },
                    {
                        "code": 130133,
                        "name": "跨境电商产品开发",
                    },
                    {
                        "code": 130132,
                        "name": "阿里国际站运营",
                    }
                ],
            },
            {
                "code": 1000250,
                "name": "业务运营",
                "subLevelModelList": [
                    {
                        "code": 130118,
                        "name": "运营助理/专员",
                    },
                    {
                        "code": 130102,
                        "name": "产品运营",
                    },
                    {
                        "code": 130101,
                        "name": "用户运营",
                    },
                    {
                        "code": 130106,
                        "name": "商家运营",
                    },
                    {
                        "code": 130103,
                        "name": "数据/策略运营",
                    },
                    {
                        "code": 130112,
                        "name": "社群运营",
                    },
                    {
                        "code": 130108,
                        "name": "游戏运营",
                    },
                    {
                        "code": 130110,
                        "name": "网站运营",
                    },
                    {
                        "code": 130120,
                        "name": "内容审核",
                    },
                    {
                        "code": 130121,
                        "name": "数据标注/AI训练师",
                    },
                    {
                        "code": 130134,
                        "name": "外卖运营",
                    }
                ],
            },
            {
                "code": 1001310,
                "name": "线下运营",
                "subLevelModelList": [
                    {
                        "code": 130116,
                        "name": "线下拓展运营",
                    },
                    {
                        "code": 290314,
                        "name": "商场运营",
                    }
                ],
            },
            {
                "code": 1000260,
                "name": "编辑",
                "subLevelModelList": [
                    {
                        "code": 130203,
                        "name": "文案编辑",
                    },
                    {
                        "code": 130201,
                        "name": "主编/副主编",
                    },
                    {
                        "code": 130204,
                        "name": "网站编辑",
                    },
                    {
                        "code": 210101,
                        "name": "医学编辑",
                    }
                ],
            },
            {
                "code": 1000280,
                "name": "高端运营职位",
                "subLevelModelList": [
                    {
                        "code": 130405,
                        "name": "运营经理/主管",
                    },
                    {
                        "code": 130402,
                        "name": "运营总监",
                    },
                    {
                        "code": 130403,
                        "name": "COO",
                    },
                    {
                        "code": 130404,
                        "name": "客服总监",
                    }
                ],
            },
            {
                "code": 1000290,
                "name": "其他运营职位",
                "subLevelModelList": [
                    {
                        "code": 130501,
                        "name": "其他运营职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1070000,
        "name": "销售",
        "subLevelModelList": [
            {
                "code": 1000490,
                "name": "销售",
                "subLevelModelList": [
                    {
                        "code": 140301,
                        "name": "销售专员",
                    },
                    {
                        "code": 140310,
                        "name": "电话销售",
                    },
                    {
                        "code": 140314,
                        "name": "网络销售",
                    },
                    {
                        "code": 140307,
                        "name": "渠道销售",
                    },
                    {
                        "code": 140304,
                        "name": "大客户代表",
                    },
                    {
                        "code": 140317,
                        "name": "客户经理",
                    },
                    {
                        "code": 140305,
                        "name": "BD经理",
                    },
                    {
                        "code": 140316,
                        "name": "销售工程师",
                    }
                ],
            },
            {
                "code": 1000520,
                "name": "销售管理",
                "subLevelModelList": [
                    {
                        "code": 140302,
                        "name": "销售经理/主管",
                    },
                    {
                        "code": 140402,
                        "name": "销售总监",
                    },
                    {
                        "code": 160103,
                        "name": "销售VP",
                    },
                    {
                        "code": 160102,
                        "name": "城市经理",
                    },
                    {
                        "code": 160101,
                        "name": "区域总监",
                    }
                ],
            },
            {
                "code": 1000420,
                "name": "销售行政/商务",
                "subLevelModelList": [
                    {
                        "code": 140309,
                        "name": "销售助理",
                    },
                    {
                        "code": 130119,
                        "name": "销售运营",
                    },
                    {
                        "code": 160301,
                        "name": "商务专员",
                    },
                    {
                        "code": 140107,
                        "name": "商务经理",
                    },
                    {
                        "code": 140403,
                        "name": "商务总监",
                    },
                    {
                        "code": 160304,
                        "name": "招商",
                    },
                    {
                        "code": 160303,
                        "name": "客户成功",
                    }
                ],
            },
            {
                "code": 1000480,
                "name": "外贸销售",
                "subLevelModelList": [
                    {
                        "code": 250203,
                        "name": "外贸业务员",
                    },
                    {
                        "code": 250201,
                        "name": "外贸经理",
                    },
                    {
                        "code": 240111,
                        "name": "货代/物流销售",
                    },
                    {
                        "code": 250205,
                        "name": "海外销售",
                    }
                ],
            },
            {
                "code": 1000500,
                "name": "教培销售",
                "subLevelModelList": [
                    {
                        "code": 190601,
                        "name": "课程顾问",
                    },
                    {
                        "code": 190603,
                        "name": "留学顾问",
                    }
                ],
            },
            {
                "code": 1000450,
                "name": "汽车销售",
                "subLevelModelList": [
                    {
                        "code": 230201,
                        "name": "汽车销售",
                    },
                    {
                        "code": 230202,
                        "name": "汽车配件销售",
                    }
                ],
            },
            {
                "code": 1000430,
                "name": "房地产销售/招商",
                "subLevelModelList": [
                    {
                        "code": 160401,
                        "name": "置业顾问",
                    },
                    {
                        "code": 160403,
                        "name": "地产中介",
                    },
                    {
                        "code": 220403,
                        "name": "地产招商",
                    }
                ],
            },
            {
                "code": 1000440,
                "name": "服务业销售",
                "subLevelModelList": [
                    {
                        "code": 290302,
                        "name": "导购",
                    },
                    {
                        "code": 160501,
                        "name": "服装导购",
                    },
                    {
                        "code": 290312,
                        "name": "珠宝销售",
                    },
                    {
                        "code": 210414,
                        "name": "美容顾问",
                    },
                    {
                        "code": 210406,
                        "name": "化妆品导购",
                    },
                    {
                        "code": 160502,
                        "name": "家装导购",
                    },
                    {
                        "code": 210610,
                        "name": "会籍顾问",
                    },
                    {
                        "code": 280103,
                        "name": "旅游顾问",
                    }
                ],
            },
            {
                "code": 1000510,
                "name": "医疗销售",
                "subLevelModelList": [
                    {
                        "code": 210502,
                        "name": "医药代表",
                    },
                    {
                        "code": 210506,
                        "name": "医疗器械销售",
                    },
                    {
                        "code": 210803,
                        "name": "药店店员",
                    },
                    {
                        "code": 210801,
                        "name": "药店店长",
                    },
                    {
                        "code": 210505,
                        "name": "医美咨询",
                    },
                    {
                        "code": 210504,
                        "name": "健康顾问",
                    },
                    {
                        "code": 210507,
                        "name": "口腔咨询师",
                    }
                ],
            },
            {
                "code": 1000460,
                "name": "广告/会展销售",
                "subLevelModelList": [
                    {
                        "code": 140313,
                        "name": "广告销售",
                    },
                    {
                        "code": 140504,
                        "name": "会展活动销售",
                    },
                    {
                        "code": 140501,
                        "name": "会议活动销售",
                    }
                ],
            },
            {
                "code": 1000470,
                "name": "金融销售",
                "subLevelModelList": [
                    {
                        "code": 180506,
                        "name": "理财顾问",
                    },
                    {
                        "code": 180701,
                        "name": "保险顾问",
                    },
                    {
                        "code": 180401,
                        "name": "信用卡销售",
                    },
                    {
                        "code": 180801,
                        "name": "证券经纪人",
                    },
                    {
                        "code": 180901,
                        "name": "机构业务经理",
                    }
                ],
            },
            {
                "code": 1000530,
                "name": "其他销售职位",
                "subLevelModelList": [
                    {
                        "code": 160201,
                        "name": "其他销售职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1060000,
        "name": "人力/行政/法务",
        "subLevelModelList": [
            {
                "code": 1000370,
                "name": "人力资源",
                "subLevelModelList": [
                    {
                        "code": 150104,
                        "name": "人力资源专员/助理",
                    },
                    {
                        "code": 150403,
                        "name": "人力资源经理/主管",
                    },
                    {
                        "code": 150108,
                        "name": "人力资源总监",
                    },
                    {
                        "code": 150102,
                        "name": "招聘",
                    },
                    {
                        "code": 150103,
                        "name": "HRBP",
                    },
                    {
                        "code": 150105,
                        "name": "培训",
                    },
                    {
                        "code": 150109,
                        "name": "员工关系",
                    },
                    {
                        "code": 150110,
                        "name": "组织发展",
                    },
                    {
                        "code": 150111,
                        "name": "企业文化",
                    },
                    {
                        "code": 150106,
                        "name": "薪酬绩效",
                    },
                    {
                        "code": 260108,
                        "name": "猎头顾问",
                    }
                ],
            },
            {
                "code": 1000380,
                "name": "行政",
                "subLevelModelList": [
                    {
                        "code": 150201,
                        "name": "行政专员/助理",
                    },
                    {
                        "code": 150401,
                        "name": "行政经理/主管",
                    },
                    {
                        "code": 150209,
                        "name": "行政总监",
                    },
                    {
                        "code": 150202,
                        "name": "前台",
                    },
                    {
                        "code": 150207,
                        "name": "后勤",
                    },
                    {
                        "code": 150205,
                        "name": "经理助理",
                    },
                    {
                        "code": 150210,
                        "name": "文员",
                    },
                    {
                        "code": 140802,
                        "name": "企业党建",
                    },
                    {
                        "code": 150211,
                        "name": "档案管理",
                    },
                    {
                        "code": 150208,
                        "name": "商务司机",
                    },
                    {
                        "code": 100603,
                        "name": "项目专员/助理",
                    }
                ],
            },
            {
                "code": 1000400,
                "name": "法律服务",
                "subLevelModelList": [
                    {
                        "code": 150203,
                        "name": "法务专员/助理",
                    },
                    {
                        "code": 150506,
                        "name": "法务经理/主管",
                    },
                    {
                        "code": 150507,
                        "name": "法务总监",
                    },
                    {
                        "code": 150504,
                        "name": "法律顾问",
                    },
                    {
                        "code": 260201,
                        "name": "律师",
                    }
                ],
            },
            {
                "code": 1000410,
                "name": "其他职能职位",
                "subLevelModelList": [
                    {
                        "code": 150601,
                        "name": "其他职能职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1230000,
        "name": "财务/审计/税务",
        "subLevelModelList": [
            {
                "code": 1001250,
                "name": "会计",
                "subLevelModelList": [
                    {
                        "code": 150301,
                        "name": "会计",
                    },
                    {
                        "code": 150311,
                        "name": "总账会计",
                    },
                    {
                        "code": 150310,
                        "name": "成本会计",
                    },
                    {
                        "code": 150304,
                        "name": "结算会计",
                    },
                    {
                        "code": 150313,
                        "name": "外勤会计",
                    },
                    {
                        "code": 150312,
                        "name": "建筑/工程会计",
                    }
                ],
            },
            {
                "code": 1001260,
                "name": "审计/税务",
                "subLevelModelList": [
                    {
                        "code": 150306,
                        "name": "审计",
                    },
                    {
                        "code": 150305,
                        "name": "税务",
                    }
                ],
            },
            {
                "code": 1001270,
                "name": "高级财务职位",
                "subLevelModelList": [
                    {
                        "code": 150402,
                        "name": "财务经理/主管",
                    },
                    {
                        "code": 150308,
                        "name": "财务总监/VP",
                    },
                    {
                        "code": 150404,
                        "name": "CFO",
                    }
                ],
            },
            {
                "code": 1000390,
                "name": "其他财务岗位",
                "subLevelModelList": [
                    {
                        "code": 150302,
                        "name": "出纳",
                    },
                    {
                        "code": 150307,
                        "name": "风控",
                    },
                    {
                        "code": 150303,
                        "name": "财务顾问",
                    },
                    {
                        "code": 150316,
                        "name": "财务分析/财务BP",
                    }
                ],
            }
        ],
    },
    {
        "code": 1190000,
        "name": "生产制造",
        "subLevelModelList": [
            {
                "code": 2600722,
                "name": "普工",
                "subLevelModelList": [
                    {
                        "code": 300601,
                        "name": "普工/操作工",
                    },
                    {
                        "code": 300624,
                        "name": "包装工",
                    },
                    {
                        "code": 300628,
                        "name": "学徒工",
                    },
                    {
                        "code": 300630,
                        "name": "搬运工/装卸工",
                    },
                    {
                        "code": 300623,
                        "name": "组装工",
                    }
                ],
            },
            {
                "code": 2600902,
                "name": "机械加工",
                "subLevelModelList": [
                    {
                        "code": 300604,
                        "name": "焊工",
                    },
                    {
                        "code": 300605,
                        "name": "氩弧焊工",
                    },
                    {
                        "code": 300610,
                        "name": "车工",
                    },
                    {
                        "code": 300613,
                        "name": "钳工",
                    },
                    {
                        "code": 300631,
                        "name": "切割工",
                    },
                    {
                        "code": 300616,
                        "name": "钣金工",
                    },
                    {
                        "code": 300622,
                        "name": "注塑工",
                    },
                    {
                        "code": 300619,
                        "name": "折弯工",
                    },
                    {
                        "code": 300611,
                        "name": "磨工",
                    },
                    {
                        "code": 300633,
                        "name": "模具工",
                    },
                    {
                        "code": 300612,
                        "name": "铣工",
                    },
                    {
                        "code": 300617,
                        "name": "抛光工",
                    },
                    {
                        "code": 300621,
                        "name": "喷塑工",
                    },
                    {
                        "code": 300614,
                        "name": "钻工",
                    },
                    {
                        "code": 300615,
                        "name": "铆工",
                    },
                    {
                        "code": 300620,
                        "name": "电镀工",
                    }
                ],
            },
            {
                "code": 1001200,
                "name": "技工",
                "subLevelModelList": [
                    {
                        "code": 300606,
                        "name": "电工",
                    },
                    {
                        "code": 300635,
                        "name": "弱电工",
                    },
                    {
                        "code": 300618,
                        "name": "机修工",
                    },
                    {
                        "code": 300311,
                        "name": "CNC数控操机/编程员",
                    },
                    {
                        "code": 300608,
                        "name": "木工",
                    },
                    {
                        "code": 300609,
                        "name": "油漆工",
                    },
                    {
                        "code": 300627,
                        "name": "锅炉工",
                    }
                ],
            },
            {
                "code": 2600732,
                "name": "运输设备操作",
                "subLevelModelList": [
                    {
                        "code": 300602,
                        "name": "叉车工",
                    },
                    {
                        "code": 300603,
                        "name": "铲车司机",
                    },
                    {
                        "code": 300634,
                        "name": "挖掘机司机",
                    }
                ],
            },
            {
                "code": 1001130,
                "name": "质量管理",
                "subLevelModelList": [
                    {
                        "code": 300201,
                        "name": "质量管理/测试工程师",
                    },
                    {
                        "code": 300208,
                        "name": "质检员",
                    },
                    {
                        "code": 300402,
                        "name": "实验室技术员",
                    },
                    {
                        "code": 300205,
                        "name": "体系工程师",
                    },
                    {
                        "code": 300206,
                        "name": "体系审核员",
                    },
                    {
                        "code": 300204,
                        "name": "产品认证工程师",
                    },
                    {
                        "code": 300203,
                        "name": "失效分析工程师",
                    },
                    {
                        "code": 300202,
                        "name": "可靠度工程师",
                    },
                    {
                        "code": 250108,
                        "name": "供应商质量工程师",
                    },
                    {
                        "code": 210122,
                        "name": "医疗器械生产/质量管理",
                    },
                    {
                        "code": 230109,
                        "name": "汽车质量工程师",
                    },
                    {
                        "code": 300209,
                        "name": "计量工程师",
                    }
                ],
            },
            {
                "code": 1001170,
                "name": "机械设计/制造",
                "subLevelModelList": [
                    {
                        "code": 300301,
                        "name": "机械工程师",
                    },
                    {
                        "code": 300306,
                        "name": "机械结构工程师",
                    },
                    {
                        "code": 300321,
                        "name": "家电/3C结构工程师",
                    },
                    {
                        "code": 300308,
                        "name": "工艺工程师",
                    },
                    {
                        "code": 300304,
                        "name": "设备维修保养工程师",
                    },
                    {
                        "code": 300303,
                        "name": "机械设备工程师",
                    },
                    {
                        "code": 300310,
                        "name": "机电工程师",
                    },
                    {
                        "code": 300305,
                        "name": "机械制图员",
                    },
                    {
                        "code": 300314,
                        "name": "模具工程师",
                    },
                    {
                        "code": 300313,
                        "name": "夹具工程师",
                    },
                    {
                        "code": 300309,
                        "name": "材料工程师",
                    },
                    {
                        "code": 300307,
                        "name": "工业工程师(IE)",
                    },
                    {
                        "code": 300319,
                        "name": "仿真工程师",
                    },
                    {
                        "code": 100813,
                        "name": "热设计工程师",
                    },
                    {
                        "code": 300316,
                        "name": "注塑工程师",
                    },
                    {
                        "code": 300315,
                        "name": "焊接工程师",
                    },
                    {
                        "code": 300317,
                        "name": "铸造/锻造工程师",
                    },
                    {
                        "code": 300318,
                        "name": "液压工程师",
                    },
                    {
                        "code": 300312,
                        "name": "冲压工程师",
                    },
                    {
                        "code": 300320,
                        "name": "装配工程师",
                    },
                    {
                        "code": 300802,
                        "name": "电机工程师",
                    },
                    {
                        "code": 300322,
                        "name": "生产制造项目经理",
                    }
                ],
            },
            {
                "code": 2600942,
                "name": "电气/自动化",
                "subLevelModelList": [
                    {
                        "code": 101402,
                        "name": "电气工程师",
                    },
                    {
                        "code": 101404,
                        "name": "电气设计工程师",
                    },
                    {
                        "code": 100803,
                        "name": "自动化工程师",
                    },
                    {
                        "code": 101414,
                        "name": "电力工程师",
                    }
                ],
            },
            {
                "code": 1001120,
                "name": "生产营运",
                "subLevelModelList": [
                    {
                        "code": 300103,
                        "name": "车间主任",
                    },
                    {
                        "code": 300104,
                        "name": "生产组长/拉长",
                    },
                    {
                        "code": 300102,
                        "name": "生产总监",
                    },
                    {
                        "code": 300101,
                        "name": "厂长",
                    },
                    {
                        "code": 300108,
                        "name": "生产跟单/文员",
                    },
                    {
                        "code": 300107,
                        "name": "生产计划/物料管理(PMC)",
                    },
                    {
                        "code": 300106,
                        "name": "生产设备管理",
                    },
                    {
                        "code": 300110,
                        "name": "厂务",
                    }
                ],
            },
            {
                "code": 2600712,
                "name": "生产安全",
                "subLevelModelList": [
                    {
                        "code": 300207,
                        "name": "生产安全员",
                    },
                    {
                        "code": 300903,
                        "name": "EHS工程师",
                    },
                    {
                        "code": 300210,
                        "name": "安全评价师",
                    }
                ],
            },
            {
                "code": 1001180,
                "name": "化工",
                "subLevelModelList": [
                    {
                        "code": 300402,
                        "name": "实验室技术员",
                    },
                    {
                        "code": 300401,
                        "name": "化工工程师",
                    },
                    {
                        "code": 300406,
                        "name": "食品/饮料研发",
                    },
                    {
                        "code": 300405,
                        "name": "化妆品研发",
                    },
                    {
                        "code": 300404,
                        "name": "涂料研发",
                    },
                    {
                        "code": 300407,
                        "name": "化工项目经理",
                    }
                ],
            },
            {
                "code": 1001190,
                "name": "服装/纺织/皮革",
                "subLevelModelList": [
                    {
                        "code": 300510,
                        "name": "服装/纺织/皮革跟单",
                    },
                    {
                        "code": 300509,
                        "name": "打样/制版",
                    },
                    {
                        "code": 300501,
                        "name": "服装/纺织设计",
                    },
                    {
                        "code": 120615,
                        "name": "鞋类设计师",
                    },
                    {
                        "code": 300507,
                        "name": "面料辅料开发",
                    },
                    {
                        "code": 300629,
                        "name": "缝纫工",
                    },
                    {
                        "code": 300632,
                        "name": "样衣工",
                    },
                    {
                        "code": 300511,
                        "name": "量体师",
                    },
                    {
                        "code": 300637,
                        "name": "裁剪工",
                    }
                ],
            },
            {
                "code": 1001140,
                "name": "新能源汽车",
                "subLevelModelList": [
                    {
                        "code": 300801,
                        "name": "电池工程师",
                    },
                    {
                        "code": 300802,
                        "name": "电机工程师",
                    }
                ],
            },
            {
                "code": 1001150,
                "name": "汽车研发/制造",
                "subLevelModelList": [
                    {
                        "code": 230108,
                        "name": "汽车项目管理",
                    },
                    {
                        "code": 230109,
                        "name": "汽车质量工程师",
                    },
                    {
                        "code": 230113,
                        "name": "汽车工艺工程师",
                    },
                    {
                        "code": 230101,
                        "name": "汽车设计",
                    },
                    {
                        "code": 300803,
                        "name": "线束设计",
                    },
                    {
                        "code": 230112,
                        "name": "车身工程师",
                    },
                    {
                        "code": 230102,
                        "name": "汽车造型设计",
                    },
                    {
                        "code": 230107,
                        "name": "汽车零部件设计",
                    },
                    {
                        "code": 230106,
                        "name": "汽车电子工程师",
                    },
                    {
                        "code": 101308,
                        "name": "自动驾驶系统工程师",
                    },
                    {
                        "code": 230105,
                        "name": "动力系统工程师",
                    },
                    {
                        "code": 230110,
                        "name": "内外饰设计工程师",
                    },
                    {
                        "code": 230103,
                        "name": "底盘工程师",
                    },
                    {
                        "code": 230111,
                        "name": "总布置工程师",
                    }
                ],
            },
            {
                "code": 2600602,
                "name": "环保",
                "subLevelModelList": [
                    {
                        "code": 300903,
                        "name": "EHS工程师",
                    },
                    {
                        "code": 300905,
                        "name": "环境采样/检测员",
                    },
                    {
                        "code": 300902,
                        "name": "环评工程师",
                    },
                    {
                        "code": 300901,
                        "name": "环保工程师",
                    },
                    {
                        "code": 300904,
                        "name": "碳排放管理师",
                    }
                ],
            },
            {
                "code": 1001210,
                "name": "其他生产制造职位",
                "subLevelModelList": [
                    {
                        "code": 300701,
                        "name": "其他生产制造职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1180000,
        "name": "零售/生活服务",
        "subLevelModelList": [
            {
                "code": 1001050,
                "name": "零售",
                "subLevelModelList": [
                    {
                        "code": 290303,
                        "name": "店员/营业员",
                    },
                    {
                        "code": 290201,
                        "name": "收银",
                    },
                    {
                        "code": 290302,
                        "name": "导购",
                    },
                    {
                        "code": 160501,
                        "name": "服装导购",
                    },
                    {
                        "code": 290312,
                        "name": "珠宝销售",
                    },
                    {
                        "code": 210406,
                        "name": "化妆品导购",
                    },
                    {
                        "code": 160502,
                        "name": "家装导购",
                    },
                    {
                        "code": 290307,
                        "name": "理货/陈列员",
                    },
                    {
                        "code": 290311,
                        "name": "促销员",
                    },
                    {
                        "code": 290308,
                        "name": "防损员",
                    },
                    {
                        "code": 290304,
                        "name": "门店店长",
                    },
                    {
                        "code": 290226,
                        "name": "储备店长",
                    },
                    {
                        "code": 290305,
                        "name": "督导/巡店",
                    },
                    {
                        "code": 290309,
                        "name": "卖场经理",
                    },
                    {
                        "code": 290314,
                        "name": "商场运营",
                    }
                ],
            },
            {
                "code": 1001060,
                "name": "美容美发",
                "subLevelModelList": [
                    {
                        "code": 210405,
                        "name": "美容师",
                    },
                    {
                        "code": 210410,
                        "name": "美容店长",
                    },
                    {
                        "code": 210408,
                        "name": "美体师",
                    },
                    {
                        "code": 210414,
                        "name": "美容顾问",
                    },
                    {
                        "code": 290802,
                        "name": "美容导师",
                    },
                    {
                        "code": 210608,
                        "name": "美甲美睫师",
                    },
                    {
                        "code": 210407,
                        "name": "纹绣师",
                    },
                    {
                        "code": 210607,
                        "name": "发型师",
                    },
                    {
                        "code": 210409,
                        "name": "美发助理/学徒",
                    },
                    {
                        "code": 290801,
                        "name": "养发师",
                    },
                    {
                        "code": 210609,
                        "name": "化妆/造型/服装",
                    }
                ],
            },
            {
                "code": 2600592,
                "name": "理疗保健",
                "subLevelModelList": [
                    {
                        "code": 210403,
                        "name": "理疗师",
                    },
                    {
                        "code": 290118,
                        "name": "产后康复师",
                    },
                    {
                        "code": 210404,
                        "name": "针灸推拿",
                    },
                    {
                        "code": 210412,
                        "name": "按摩师",
                    },
                    {
                        "code": 210411,
                        "name": "足疗师",
                    },
                    {
                        "code": 210415,
                        "name": "采耳师",
                    },
                    {
                        "code": 210401,
                        "name": "营养师/健康管理师",
                    },
                    {
                        "code": 210305,
                        "name": "康复治疗师",
                    }
                ],
            },
            {
                "code": 1001100,
                "name": "家政/保洁",
                "subLevelModelList": [
                    {
                        "code": 290106,
                        "name": "保洁",
                    },
                    {
                        "code": 290122,
                        "name": "保洁主管",
                    },
                    {
                        "code": 290108,
                        "name": "保姆",
                    },
                    {
                        "code": 290109,
                        "name": "月嫂",
                    },
                    {
                        "code": 290110,
                        "name": "育婴师",
                    },
                    {
                        "code": 290111,
                        "name": "护工",
                    },
                    {
                        "code": 290169,
                        "name": "收纳师",
                    }
                ],
            },
            {
                "code": 2600572,
                "name": "安保服务",
                "subLevelModelList": [
                    {
                        "code": 290105,
                        "name": "保安",
                    },
                    {
                        "code": 290117,
                        "name": "保安主管/队长",
                    },
                    {
                        "code": 290121,
                        "name": "消防中控员",
                    },
                    {
                        "code": 290120,
                        "name": "押运员",
                    },
                    {
                        "code": 290112,
                        "name": "安检员",
                    },
                    {
                        "code": 290123,
                        "name": "消防维保员",
                    }
                ],
            },
            {
                "code": 2600972,
                "name": "维修服务",
                "subLevelModelList": [
                    {
                        "code": 290114,
                        "name": "家电维修",
                    },
                    {
                        "code": 290113,
                        "name": "手机维修",
                    },
                    {
                        "code": 290166,
                        "name": "电脑/打印机维修",
                    },
                    {
                        "code": 290124,
                        "name": "电动车/摩托车维修",
                    }
                ],
            },
            {
                "code": 1001160,
                "name": "汽车服务",
                "subLevelModelList": [
                    {
                        "code": 230204,
                        "name": "汽车维修",
                    },
                    {
                        "code": 230205,
                        "name": "汽车美容",
                    },
                    {
                        "code": 230213,
                        "name": "洗车工",
                    },
                    {
                        "code": 230209,
                        "name": "汽车改装",
                    },
                    {
                        "code": 230203,
                        "name": "汽车服务顾问",
                    },
                    {
                        "code": 230208,
                        "name": "4S店店长/维修站长",
                    },
                    {
                        "code": 230207,
                        "name": "二手车评估师",
                    },
                    {
                        "code": 230206,
                        "name": "汽车查勘定损",
                    },
                    {
                        "code": 230214,
                        "name": "加油员",
                    }
                ],
            },
            {
                "code": 1001090,
                "name": "宠物服务",
                "subLevelModelList": [
                    {
                        "code": 290601,
                        "name": "宠物美容",
                    },
                    {
                        "code": 290602,
                        "name": "宠物医生",
                    }
                ],
            },
            {
                "code": 1001070,
                "name": "运动健身",
                "subLevelModelList": [
                    {
                        "code": 190705,
                        "name": "健身教练",
                    },
                    {
                        "code": 190701,
                        "name": "舞蹈老师",
                    },
                    {
                        "code": 190706,
                        "name": "篮球教练",
                    },
                    {
                        "code": 210601,
                        "name": "瑜伽老师",
                    },
                    {
                        "code": 210603,
                        "name": "游泳教练",
                    },
                    {
                        "code": 190707,
                        "name": "跆拳道教练",
                    },
                    {
                        "code": 190708,
                        "name": "武术教练",
                    },
                    {
                        "code": 190709,
                        "name": "轮滑教练",
                    },
                    {
                        "code": 210613,
                        "name": "救生员",
                    },
                    {
                        "code": 190719,
                        "name": "乒乓球教练",
                    },
                    {
                        "code": 190766,
                        "name": "足球教练",
                    },
                    {
                        "code": 190720,
                        "name": "羽毛球教练",
                    },
                    {
                        "code": 190769,
                        "name": "拳击教练",
                    },
                    {
                        "code": 190312,
                        "name": "体育/体能老师",
                    }
                ],
            },
            {
                "code": 2600752,
                "name": "驾驶员",
                "subLevelModelList": [
                    {
                        "code": 240305,
                        "name": "网约车司机",
                    },
                    {
                        "code": 240306,
                        "name": "代驾司机",
                    },
                    {
                        "code": 240307,
                        "name": "驾校教练",
                    },
                    {
                        "code": 150208,
                        "name": "商务司机",
                    },
                    {
                        "code": 240301,
                        "name": "货运司机",
                    },
                    {
                        "code": 240308,
                        "name": "客运司机",
                    },
                    {
                        "code": 100311,
                        "name": "无人机飞手",
                    }
                ],
            },
            {
                "code": 1001110,
                "name": "其他服务业职位",
                "subLevelModelList": [
                    {
                        "code": 290701,
                        "name": "花艺师",
                    },
                    {
                        "code": 290702,
                        "name": "婚礼策划",
                    },
                    {
                        "code": 290313,
                        "name": "网吧网管",
                    },
                    {
                        "code": 210610,
                        "name": "会籍顾问",
                    },
                    {
                        "code": 280103,
                        "name": "旅游顾问",
                    },
                    {
                        "code": 210109,
                        "name": "验光师",
                    },
                    {
                        "code": 170602,
                        "name": "摄影/摄像师",
                    },
                    {
                        "code": 170626,
                        "name": "剧本杀主持人",
                    },
                    {
                        "code": 170628,
                        "name": "儿童引导师",
                    },
                    {
                        "code": 170613,
                        "name": "放映员",
                    },
                    {
                        "code": 290402,
                        "name": "游戏陪玩",
                    },
                    {
                        "code": 290401,
                        "name": "其他服务业职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 2142200,
        "name": "餐饮",
        "subLevelModelList": [
            {
                "code": 2600512,
                "name": "前厅",
                "subLevelModelList": [
                    {
                        "code": 290202,
                        "name": "服务员",
                    },
                    {
                        "code": 290201,
                        "name": "收银",
                    },
                    {
                        "code": 290107,
                        "name": "礼仪/迎宾/接待",
                    },
                    {
                        "code": 290216,
                        "name": "传菜员",
                    }
                ],
            },
            {
                "code": 2600522,
                "name": "后厨",
                "subLevelModelList": [
                    {
                        "code": 290212,
                        "name": "餐饮学徒",
                    },
                    {
                        "code": 290203,
                        "name": "厨师",
                    },
                    {
                        "code": 290219,
                        "name": "中餐厨师",
                    },
                    {
                        "code": 290222,
                        "name": "烧烤师傅",
                    },
                    {
                        "code": 290220,
                        "name": "西餐厨师",
                    },
                    {
                        "code": 290221,
                        "name": "日料厨师",
                    },
                    {
                        "code": 290218,
                        "name": "凉菜厨师",
                    },
                    {
                        "code": 290213,
                        "name": "面点师",
                    },
                    {
                        "code": 290208,
                        "name": "后厨",
                    },
                    {
                        "code": 290209,
                        "name": "配菜打荷",
                    },
                    {
                        "code": 290217,
                        "name": "洗碗工",
                    },
                    {
                        "code": 290224,
                        "name": "水台/水产员",
                    }
                ],
            },
            {
                "code": 2600532,
                "name": "餐饮管理",
                "subLevelModelList": [
                    {
                        "code": 290206,
                        "name": "餐饮店长",
                    },
                    {
                        "code": 290207,
                        "name": "餐饮前厅经理/领班",
                    },
                    {
                        "code": 290226,
                        "name": "储备店长",
                    },
                    {
                        "code": 290215,
                        "name": "厨师长",
                    },
                    {
                        "code": 290214,
                        "name": "行政总厨",
                    },
                    {
                        "code": 290228,
                        "name": "餐饮督导",
                    }
                ],
            },
            {
                "code": 2600552,
                "name": "甜点饮品",
                "subLevelModelList": [
                    {
                        "code": 290204,
                        "name": "咖啡师",
                    },
                    {
                        "code": 290210,
                        "name": "茶艺师",
                    },
                    {
                        "code": 290223,
                        "name": "奶茶店店员",
                    },
                    {
                        "code": 290227,
                        "name": "调酒师",
                    },
                    {
                        "code": 290225,
                        "name": "面包/烘焙师",
                    },
                    {
                        "code": 290211,
                        "name": "蛋糕/裱花师",
                    }
                ],
            },
            {
                "code": 2600542,
                "name": "其他餐饮岗位",
                "subLevelModelList": [
                    {
                        "code": 290205,
                        "name": "送餐员",
                    },
                    {
                        "code": 130134,
                        "name": "外卖运营",
                    }
                ],
            }
        ],
    },
    {
        "code": 1170000,
        "name": "酒店/旅游",
        "subLevelModelList": [
            {
                "code": 1001040,
                "name": "酒店",
                "subLevelModelList": [
                    {
                        "code": 290102,
                        "name": "酒店前台",
                    },
                    {
                        "code": 290107,
                        "name": "礼仪/迎宾/接待",
                    },
                    {
                        "code": 290103,
                        "name": "客房服务员",
                    },
                    {
                        "code": 290104,
                        "name": "酒店经理",
                    },
                    {
                        "code": 290115,
                        "name": "酒店前厅经理",
                    },
                    {
                        "code": 290116,
                        "name": "客房经理",
                    },
                    {
                        "code": 290158,
                        "name": "民宿管家",
                    }
                ],
            },
            {
                "code": 1001000,
                "name": "旅游服务",
                "subLevelModelList": [
                    {
                        "code": 280103,
                        "name": "旅游顾问",
                    },
                    {
                        "code": 280104,
                        "name": "导游",
                    },
                    {
                        "code": 280201,
                        "name": "旅游产品经理",
                    },
                    {
                        "code": 280106,
                        "name": "讲解员",
                    },
                    {
                        "code": 280101,
                        "name": "计调",
                    },
                    {
                        "code": 280105,
                        "name": "票务员",
                    },
                    {
                        "code": 280102,
                        "name": "签证专员",
                    }
                ],
            },
            {
                "code": 1001020,
                "name": "其他旅游职位",
                "subLevelModelList": [
                    {
                        "code": 280301,
                        "name": "其他旅游职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1100000,
        "name": "教育培训",
        "subLevelModelList": [
            {
                "code": 1000680,
                "name": "教师",
                "subLevelModelList": [
                    {
                        "code": 190301,
                        "name": "教师",
                    },
                    {
                        "code": 190309,
                        "name": "英语教师",
                    },
                    {
                        "code": 190302,
                        "name": "助教",
                    },
                    {
                        "code": 190317,
                        "name": "数学教师",
                    },
                    {
                        "code": 190316,
                        "name": "语文教师",
                    },
                    {
                        "code": 190303,
                        "name": "高中教师",
                    },
                    {
                        "code": 190304,
                        "name": "初中教师",
                    },
                    {
                        "code": 190305,
                        "name": "小学教师",
                    },
                    {
                        "code": 190321,
                        "name": "家教",
                    },
                    {
                        "code": 190318,
                        "name": "物理教师",
                    },
                    {
                        "code": 190319,
                        "name": "化学教师",
                    },
                    {
                        "code": 190307,
                        "name": "理科教师",
                    },
                    {
                        "code": 190314,
                        "name": "日语教师",
                    },
                    {
                        "code": 190320,
                        "name": "生物教师",
                    },
                    {
                        "code": 190308,
                        "name": "文科教师",
                    },
                    {
                        "code": 190245,
                        "name": "地理教师",
                    },
                    {
                        "code": 190315,
                        "name": "其他外语教师",
                    }
                ],
            },
            {
                "code": 2600892,
                "name": "幼少儿教师",
                "subLevelModelList": [
                    {
                        "code": 190306,
                        "name": "幼教",
                    },
                    {
                        "code": 190322,
                        "name": "托管老师",
                    },
                    {
                        "code": 190323,
                        "name": "早教老师",
                    },
                    {
                        "code": 190326,
                        "name": "保育员",
                    },
                    {
                        "code": 190324,
                        "name": "感统训练教师",
                    }
                ],
            },
            {
                "code": 1000670,
                "name": "教育行政",
                "subLevelModelList": [
                    {
                        "code": 190202,
                        "name": "教务管理",
                    },
                    {
                        "code": 190201,
                        "name": "校长/副校长",
                    },
                    {
                        "code": 190204,
                        "name": "班主任/辅导员",
                    },
                    {
                        "code": 190203,
                        "name": "教学管理",
                    },
                    {
                        "code": 190205,
                        "name": "园长/副园长",
                    },
                    {
                        "code": 190313,
                        "name": "就业老师",
                    }
                ],
            },
            {
                "code": 2600812,
                "name": "运动健身",
                "subLevelModelList": [
                    {
                        "code": 190705,
                        "name": "健身教练",
                    },
                    {
                        "code": 190706,
                        "name": "篮球教练",
                    },
                    {
                        "code": 190707,
                        "name": "跆拳道教练",
                    },
                    {
                        "code": 190708,
                        "name": "武术教练",
                    },
                    {
                        "code": 190709,
                        "name": "轮滑教练",
                    },
                    {
                        "code": 190719,
                        "name": "乒乓球教练",
                    },
                    {
                        "code": 190766,
                        "name": "足球教练",
                    },
                    {
                        "code": 190720,
                        "name": "羽毛球教练",
                    },
                    {
                        "code": 190769,
                        "name": "拳击教练",
                    },
                    {
                        "code": 190770,
                        "name": "台球教练/助教",
                    },
                    {
                        "code": 210603,
                        "name": "游泳教练",
                    },
                    {
                        "code": 210601,
                        "name": "瑜伽老师",
                    },
                    {
                        "code": 210613,
                        "name": "救生员",
                    },
                    {
                        "code": 210612,
                        "name": "普拉提老师",
                    },
                    {
                        "code": 190312,
                        "name": "体育/体能老师",
                    }
                ],
            },
            {
                "code": 2600822,
                "name": "文化艺术",
                "subLevelModelList": [
                    {
                        "code": 190311,
                        "name": "美术教师",
                    },
                    {
                        "code": 190701,
                        "name": "舞蹈老师",
                    },
                    {
                        "code": 190712,
                        "name": "书法教师",
                    },
                    {
                        "code": 190310,
                        "name": "音乐教师",
                    },
                    {
                        "code": 190716,
                        "name": "播音主持教师",
                    },
                    {
                        "code": 190713,
                        "name": "钢琴教师",
                    },
                    {
                        "code": 190715,
                        "name": "古筝教师",
                    },
                    {
                        "code": 190714,
                        "name": "吉他教师",
                    },
                    {
                        "code": 190710,
                        "name": "表演教师",
                    },
                    {
                        "code": 190767,
                        "name": "架子鼓老师",
                    },
                    {
                        "code": 190768,
                        "name": "围棋老师",
                    }
                ],
            },
            {
                "code": 2600832,
                "name": "科学探索培训",
                "subLevelModelList": [
                    {
                        "code": 190717,
                        "name": "乐高教师",
                    },
                    {
                        "code": 190711,
                        "name": "机器人教师",
                    },
                    {
                        "code": 190718,
                        "name": "少儿编程老师",
                    }
                ],
            },
            {
                "code": 1000700,
                "name": "职业培训",
                "subLevelModelList": [
                    {
                        "code": 190105,
                        "name": "培训师",
                    },
                    {
                        "code": 190504,
                        "name": "拓展培训",
                    },
                    {
                        "code": 190406,
                        "name": "IT培训讲师",
                    },
                    {
                        "code": 190501,
                        "name": "财会培训讲师",
                    }
                ],
            },
            {
                "code": 1000660,
                "name": "教育产品研发",
                "subLevelModelList": [
                    {
                        "code": 190101,
                        "name": "课程设计",
                    },
                    {
                        "code": 190102,
                        "name": "课程编辑",
                    },
                    {
                        "code": 190107,
                        "name": "培训策划",
                    },
                    {
                        "code": 190104,
                        "name": "培训研究",
                    },
                    {
                        "code": 190106,
                        "name": "其他教育产品研发职位",
                    }
                ],
            },
            {
                "code": 2600452,
                "name": "教培销售",
                "subLevelModelList": [
                    {
                        "code": 190601,
                        "name": "课程顾问",
                    },
                    {
                        "code": 190603,
                        "name": "留学顾问",
                    }
                ],
            },
            {
                "code": 1000720,
                "name": "其他教育培训职位",
                "subLevelModelList": [
                    {
                        "code": 190801,
                        "name": "其他教育培训职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1030000,
        "name": "设计",
        "subLevelModelList": [
            {
                "code": 1000190,
                "name": "视觉/交互设计",
                "subLevelModelList": [
                    {
                        "code": 120106,
                        "name": "平面设计",
                    },
                    {
                        "code": 120117,
                        "name": "美工",
                    },
                    {
                        "code": 120119,
                        "name": "设计师助理",
                    },
                    {
                        "code": 120105,
                        "name": "UI设计师",
                    },
                    {
                        "code": 120101,
                        "name": "视觉设计师",
                    },
                    {
                        "code": 140603,
                        "name": "广告设计",
                    },
                    {
                        "code": 120201,
                        "name": "UX/交互设计师",
                    },
                    {
                        "code": 120302,
                        "name": "用户研究",
                    },
                    {
                        "code": 120118,
                        "name": "包装设计",
                    },
                    {
                        "code": 120123,
                        "name": "修图师",
                    }
                ],
            },
            {
                "code": 2600652,
                "name": "环境设计",
                "subLevelModelList": [
                    {
                        "code": 220205,
                        "name": "室内设计",
                    },
                    {
                        "code": 220217,
                        "name": "软装设计师",
                    },
                    {
                        "code": 120611,
                        "name": "展览/展示设计",
                    },
                    {
                        "code": 120608,
                        "name": "陈列设计",
                    },
                    {
                        "code": 120612,
                        "name": "照明设计",
                    },
                    {
                        "code": 220206,
                        "name": "园林/景观设计",
                    },
                    {
                        "code": 120614,
                        "name": "舞美设计师",
                    },
                    {
                        "code": 120116,
                        "name": "CAD绘图员",
                    }
                ],
            },
            {
                "code": 2600962,
                "name": "工业/产品设计",
                "subLevelModelList": [
                    {
                        "code": 120604,
                        "name": "家具设计",
                    },
                    {
                        "code": 120602,
                        "name": "工业设计",
                    },
                    {
                        "code": 120606,
                        "name": "珠宝设计",
                    },
                    {
                        "code": 120613,
                        "name": "家具拆单员",
                    },
                    {
                        "code": 300501,
                        "name": "服装/纺织设计",
                    },
                    {
                        "code": 120615,
                        "name": "鞋类设计师",
                    }
                ],
            },
            {
                "code": 2600622,
                "name": "美术/3D/动画",
                "subLevelModelList": [
                    {
                        "code": 120107,
                        "name": "3D设计师",
                    },
                    {
                        "code": 120121,
                        "name": "插画师",
                    },
                    {
                        "code": 120120,
                        "name": "动画设计",
                    },
                    {
                        "code": 120110,
                        "name": "原画师",
                    },
                    {
                        "code": 120122,
                        "name": "漫画师",
                    }
                ],
            },
            {
                "code": 1000200,
                "name": "游戏设计",
                "subLevelModelList": [
                    {
                        "code": 120113,
                        "name": "游戏场景",
                    },
                    {
                        "code": 120114,
                        "name": "游戏角色",
                    },
                    {
                        "code": 120112,
                        "name": "游戏UI设计",
                    },
                    {
                        "code": 120111,
                        "name": "游戏特效",
                    },
                    {
                        "code": 120115,
                        "name": "游戏动作",
                    },
                    {
                        "code": 120306,
                        "name": "游戏主美术",
                    }
                ],
            },
            {
                "code": 1000220,
                "name": "设计管理",
                "subLevelModelList": [
                    {
                        "code": 120401,
                        "name": "设计总监/经理",
                    },
                    {
                        "code": 120404,
                        "name": "视觉设计总监/经理",
                    }
                ],
            }
        ],
    },
    {
        "code": 1140000,
        "name": "房地产/建筑",
        "subLevelModelList": [
            {
                "code": 2600672,
                "name": "工程管理",
                "subLevelModelList": [
                    {
                        "code": 220212,
                        "name": "建筑施工项目经理",
                    },
                    {
                        "code": 220209,
                        "name": "工程造价",
                    },
                    {
                        "code": 220208,
                        "name": "工程监理",
                    },
                    {
                        "code": 260114,
                        "name": "工程咨询",
                    },
                    {
                        "code": 220218,
                        "name": "施工员",
                    },
                    {
                        "code": 220211,
                        "name": "资料员",
                    },
                    {
                        "code": 220220,
                        "name": "材料员",
                    },
                    {
                        "code": 220219,
                        "name": "测绘/测量",
                    },
                    {
                        "code": 220225,
                        "name": "施工安全员",
                    },
                    {
                        "code": 220226,
                        "name": "工程检测员",
                    },
                    {
                        "code": 220103,
                        "name": "工程招投标",
                    }
                ],
            },
            {
                "code": 1000900,
                "name": "装饰装修",
                "subLevelModelList": [
                    {
                        "code": 220205,
                        "name": "室内设计",
                    },
                    {
                        "code": 220217,
                        "name": "软装设计师",
                    },
                    {
                        "code": 220222,
                        "name": "装修项目经理",
                    },
                    {
                        "code": 220702,
                        "name": "装修监理",
                    },
                    {
                        "code": 220701,
                        "name": "装修造价",
                    }
                ],
            },
            {
                "code": 1000910,
                "name": "物业管理",
                "subLevelModelList": [
                    {
                        "code": 220401,
                        "name": "物业经理",
                    },
                    {
                        "code": 220406,
                        "name": "物业管理员",
                    },
                    {
                        "code": 220404,
                        "name": "综合维修工",
                    },
                    {
                        "code": 220407,
                        "name": "物业工程主管",
                    },
                    {
                        "code": 300635,
                        "name": "弱电工",
                    },
                    {
                        "code": 220405,
                        "name": "绿化工",
                    },
                    {
                        "code": 290105,
                        "name": "保安",
                    },
                    {
                        "code": 290117,
                        "name": "保安主管/队长",
                    },
                    {
                        "code": 290121,
                        "name": "消防中控员",
                    },
                    {
                        "code": 290123,
                        "name": "消防维保员",
                    }
                ],
            },
            {
                "code": 2600692,
                "name": "建筑/规划设计",
                "subLevelModelList": [
                    {
                        "code": 220204,
                        "name": "土木/土建/结构工程师",
                    },
                    {
                        "code": 220206,
                        "name": "园林/景观设计",
                    },
                    {
                        "code": 220203,
                        "name": "建筑设计师",
                    },
                    {
                        "code": 220202,
                        "name": "建筑工程师",
                    },
                    {
                        "code": 220213,
                        "name": "弱电工程师",
                    },
                    {
                        "code": 220223,
                        "name": "建筑机电工程师",
                    },
                    {
                        "code": 220214,
                        "name": "给排水工程师",
                    },
                    {
                        "code": 220215,
                        "name": "暖通工程师",
                    },
                    {
                        "code": 220216,
                        "name": "幕墙工程师",
                    },
                    {
                        "code": 220221,
                        "name": "BIM工程师",
                    },
                    {
                        "code": 220224,
                        "name": "消防工程师",
                    },
                    {
                        "code": 301004,
                        "name": "水利工程师",
                    },
                    {
                        "code": 220207,
                        "name": "城乡规划设计",
                    }
                ],
            },
            {
                "code": 1000890,
                "name": "房地产规划开发",
                "subLevelModelList": [
                    {
                        "code": 220102,
                        "name": "房地产项目管理",
                    },
                    {
                        "code": 220101,
                        "name": "房地产策划",
                    },
                    {
                        "code": 220302,
                        "name": "房地产估价师",
                    }
                ],
            },
            {
                "code": 2600442,
                "name": "房地产销售/招商",
                "subLevelModelList": [
                    {
                        "code": 160401,
                        "name": "置业顾问",
                    },
                    {
                        "code": 160403,
                        "name": "地产中介",
                    },
                    {
                        "code": 220403,
                        "name": "地产招商",
                    }
                ],
            },
            {
                "code": 1001280,
                "name": "建筑/装修工人",
                "subLevelModelList": [
                    {
                        "code": 300606,
                        "name": "电工",
                    },
                    {
                        "code": 300604,
                        "name": "焊工",
                    },
                    {
                        "code": 300608,
                        "name": "木工",
                    },
                    {
                        "code": 300625,
                        "name": "空调工",
                    },
                    {
                        "code": 300609,
                        "name": "油漆工",
                    },
                    {
                        "code": 300626,
                        "name": "电梯工",
                    },
                    {
                        "code": 300639,
                        "name": "泥瓦工",
                    },
                    {
                        "code": 300638,
                        "name": "水电工",
                    }
                ],
            },
            {
                "code": 1000930,
                "name": "其他房地产职位",
                "subLevelModelList": [
                    {
                        "code": 220601,
                        "name": "其他房地产职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1080000,
        "name": "直播/影视/传媒",
        "subLevelModelList": [
            {
                "code": 2600872,
                "name": "直播",
                "subLevelModelList": [
                    {
                        "code": 170610,
                        "name": "主播",
                    },
                    {
                        "code": 170625,
                        "name": "带货主播",
                    },
                    {
                        "code": 130122,
                        "name": "直播运营",
                    },
                    {
                        "code": 170629,
                        "name": "游戏主播",
                    },
                    {
                        "code": 170621,
                        "name": "中控/场控/助播",
                    }
                ],
            },
            {
                "code": 1000570,
                "name": "影视",
                "subLevelModelList": [
                    {
                        "code": 210609,
                        "name": "化妆/造型/服装",
                    },
                    {
                        "code": 170611,
                        "name": "演员/配音员",
                    },
                    {
                        "code": 170617,
                        "name": "艺人助理",
                    },
                    {
                        "code": 170620,
                        "name": "主持人/DJ",
                    },
                    {
                        "code": 170630,
                        "name": "模特",
                    },
                    {
                        "code": 170602,
                        "name": "摄影/摄像师",
                    },
                    {
                        "code": 170601,
                        "name": "导演/编导",
                    },
                    {
                        "code": 170605,
                        "name": "经纪人/星探",
                    },
                    {
                        "code": 170616,
                        "name": "编剧",
                    },
                    {
                        "code": 170615,
                        "name": "制片人",
                    },
                    {
                        "code": 170609,
                        "name": "影视策划",
                    },
                    {
                        "code": 170608,
                        "name": "影视发行",
                    },
                    {
                        "code": 170603,
                        "name": "视频剪辑",
                    },
                    {
                        "code": 170606,
                        "name": "后期制作",
                    },
                    {
                        "code": 120120,
                        "name": "动画设计",
                    },
                    {
                        "code": 120123,
                        "name": "修图师",
                    },
                    {
                        "code": 170622,
                        "name": "灯光师",
                    },
                    {
                        "code": 170614,
                        "name": "录音/音效",
                    },
                    {
                        "code": 170604,
                        "name": "音频编辑",
                    },
                    {
                        "code": 170624,
                        "name": "影视特效",
                    },
                    {
                        "code": 170626,
                        "name": "剧本杀主持人",
                    },
                    {
                        "code": 170628,
                        "name": "儿童引导师",
                    },
                    {
                        "code": 170613,
                        "name": "放映员",
                    },
                    {
                        "code": 170627,
                        "name": "剧本杀编剧",
                    },
                    {
                        "code": 120614,
                        "name": "舞美设计师",
                    }
                ],
            },
            {
                "code": 1000560,
                "name": "广告",
                "subLevelModelList": [
                    {
                        "code": 140604,
                        "name": "策划经理",
                    },
                    {
                        "code": 140605,
                        "name": "广告文案",
                    },
                    {
                        "code": 140202,
                        "name": "广告客户执行",
                    },
                    {
                        "code": 140601,
                        "name": "广告创意策划",
                    },
                    {
                        "code": 140407,
                        "name": "创意总监",
                    },
                    {
                        "code": 140603,
                        "name": "广告设计",
                    },
                    {
                        "code": 140602,
                        "name": "美术指导",
                    },
                    {
                        "code": 140607,
                        "name": "广告制作",
                    },
                    {
                        "code": 140611,
                        "name": "广告审核",
                    },
                    {
                        "code": 170212,
                        "name": "广告/会展项目经理",
                    }
                ],
            },
            {
                "code": 1000540,
                "name": "采编/写作/出版",
                "subLevelModelList": [
                    {
                        "code": 130203,
                        "name": "文案编辑",
                    },
                    {
                        "code": 170104,
                        "name": "作者/撰稿人",
                    },
                    {
                        "code": 170102,
                        "name": "编辑",
                    },
                    {
                        "code": 170109,
                        "name": "印刷排版",
                    },
                    {
                        "code": 170106,
                        "name": "校对录入",
                    },
                    {
                        "code": 170101,
                        "name": "记者/采编",
                    },
                    {
                        "code": 130204,
                        "name": "网站编辑",
                    },
                    {
                        "code": 130201,
                        "name": "主编/副主编",
                    },
                    {
                        "code": 170105,
                        "name": "出版发行",
                    },
                    {
                        "code": 170627,
                        "name": "剧本杀编剧",
                    }
                ],
            },
            {
                "code": 1000580,
                "name": "其他传媒职位",
                "subLevelModelList": [
                    {
                        "code": 170501,
                        "name": "其他传媒职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1050000,
        "name": "市场/公关/广告",
        "subLevelModelList": [
            {
                "code": 1000310,
                "name": "市场营销",
                "subLevelModelList": [
                    {
                        "code": 140101,
                        "name": "市场营销策划",
                    },
                    {
                        "code": 140111,
                        "name": "海外市场",
                    },
                    {
                        "code": 140109,
                        "name": "活动策划执行",
                    },
                    {
                        "code": 140506,
                        "name": "会务/会展执行",
                    },
                    {
                        "code": 140505,
                        "name": "会务/会展策划",
                    },
                    {
                        "code": 140401,
                        "name": "市场总监",
                    },
                    {
                        "code": 140404,
                        "name": "CMO",
                    }
                ],
            },
            {
                "code": 2600952,
                "name": "推广/投放",
                "subLevelModelList": [
                    {
                        "code": 140104,
                        "name": "市场推广/地推",
                    },
                    {
                        "code": 130109,
                        "name": "网络推广",
                    },
                    {
                        "code": 140115,
                        "name": "游戏推广",
                    },
                    {
                        "code": 140608,
                        "name": "媒介投放",
                    },
                    {
                        "code": 140609,
                        "name": "媒介商务BD",
                    },
                    {
                        "code": 140204,
                        "name": "媒介专员",
                    },
                    {
                        "code": 140206,
                        "name": "媒介经理/总监",
                    },
                    {
                        "code": 140116,
                        "name": "信息流优化师",
                    },
                    {
                        "code": 140105,
                        "name": "SEO",
                    },
                    {
                        "code": 140106,
                        "name": "SEM",
                    }
                ],
            },
            {
                "code": 1000300,
                "name": "政府事务",
                "subLevelModelList": [
                    {
                        "code": 140112,
                        "name": "政府关系",
                    },
                    {
                        "code": 140804,
                        "name": "项目申报专员",
                    },
                    {
                        "code": 140801,
                        "name": "政策研究",
                    },
                    {
                        "code": 140803,
                        "name": "社工",
                    }
                ],
            },
            {
                "code": 1000320,
                "name": "公关",
                "subLevelModelList": [
                    {
                        "code": 140203,
                        "name": "品牌公关",
                    },
                    {
                        "code": 140405,
                        "name": "公关总监",
                    }
                ],
            },
            {
                "code": 1000340,
                "name": "广告",
                "subLevelModelList": [
                    {
                        "code": 140604,
                        "name": "策划经理",
                    },
                    {
                        "code": 140605,
                        "name": "广告文案",
                    },
                    {
                        "code": 140202,
                        "name": "广告客户执行",
                    },
                    {
                        "code": 140601,
                        "name": "广告创意策划",
                    },
                    {
                        "code": 140407,
                        "name": "创意总监",
                    },
                    {
                        "code": 140603,
                        "name": "广告设计",
                    },
                    {
                        "code": 140602,
                        "name": "美术指导",
                    },
                    {
                        "code": 140607,
                        "name": "广告制作",
                    },
                    {
                        "code": 140611,
                        "name": "广告审核",
                    },
                    {
                        "code": 170212,
                        "name": "广告/会展项目经理",
                    }
                ],
            },
            {
                "code": 1001240,
                "name": "调研分析",
                "subLevelModelList": [
                    {
                        "code": 260109,
                        "name": "市场调研",
                    },
                    {
                        "code": 140114,
                        "name": "选址开发",
                    },
                    {
                        "code": 140108,
                        "name": "商业数据分析",
                    }
                ],
            },
            {
                "code": 2600462,
                "name": "广告/会展销售",
                "subLevelModelList": [
                    {
                        "code": 140313,
                        "name": "广告销售",
                    },
                    {
                        "code": 140504,
                        "name": "会展活动销售",
                    },
                    {
                        "code": 140501,
                        "name": "会议活动销售",
                    }
                ],
            },
            {
                "code": 1000360,
                "name": "其他市场职位",
                "subLevelModelList": [
                    {
                        "code": 140701,
                        "name": "其他市场职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1130000,
        "name": "物流/仓储/司机",
        "subLevelModelList": [
            {
                "code": 1000840,
                "name": "物流/运输",
                "subLevelModelList": [
                    {
                        "code": 240103,
                        "name": "物流专员",
                    },
                    {
                        "code": 240104,
                        "name": "物流经理",
                    },
                    {
                        "code": 240402,
                        "name": "物流总监",
                    },
                    {
                        "code": 240105,
                        "name": "物流运营",
                    },
                    {
                        "code": 240106,
                        "name": "物流跟单",
                    },
                    {
                        "code": 240108,
                        "name": "调度员",
                    },
                    {
                        "code": 240110,
                        "name": "运输经理/主管",
                    },
                    {
                        "code": 240119,
                        "name": "跟车员",
                    },
                    {
                        "code": 240113,
                        "name": "水/空/陆运操作",
                    },
                    {
                        "code": 240111,
                        "name": "货代/物流销售",
                    },
                    {
                        "code": 240109,
                        "name": "物流/仓储项目经理",
                    },
                    {
                        "code": 240302,
                        "name": "集装箱管理",
                    }
                ],
            },
            {
                "code": 2600742,
                "name": "配送理货",
                "subLevelModelList": [
                    {
                        "code": 240206,
                        "name": "配/理/拣/发货",
                    },
                    {
                        "code": 240303,
                        "name": "配送员",
                    },
                    {
                        "code": 290205,
                        "name": "送餐员",
                    },
                    {
                        "code": 240304,
                        "name": "快递员",
                    },
                    {
                        "code": 300630,
                        "name": "搬运工/装卸工",
                    },
                    {
                        "code": 300602,
                        "name": "叉车工",
                    },
                    {
                        "code": 240118,
                        "name": "配送站长",
                    }
                ],
            },
            {
                "code": 1000860,
                "name": "驾驶员",
                "subLevelModelList": [
                    {
                        "code": 240301,
                        "name": "货运司机",
                    },
                    {
                        "code": 150208,
                        "name": "商务司机",
                    },
                    {
                        "code": 240305,
                        "name": "网约车司机",
                    },
                    {
                        "code": 240306,
                        "name": "代驾司机",
                    },
                    {
                        "code": 240307,
                        "name": "驾校教练",
                    },
                    {
                        "code": 240308,
                        "name": "客运司机",
                    },
                    {
                        "code": 100311,
                        "name": "无人机飞手",
                    }
                ],
            },
            {
                "code": 1000850,
                "name": "仓储",
                "subLevelModelList": [
                    {
                        "code": 240204,
                        "name": "仓库管理员",
                    },
                    {
                        "code": 240205,
                        "name": "仓库文员",
                    },
                    {
                        "code": 240201,
                        "name": "仓库主管/经理",
                    }
                ],
            },
            {
                "code": 1000870,
                "name": "供应链",
                "subLevelModelList": [
                    {
                        "code": 240101,
                        "name": "供应链专员",
                    },
                    {
                        "code": 240102,
                        "name": "供应链经理",
                    },
                    {
                        "code": 240401,
                        "name": "供应链总监",
                    },
                    {
                        "code": 300107,
                        "name": "生产计划/物料管理(PMC)",
                    }
                ],
            }
        ],
    },
    {
        "code": 1120000,
        "name": "采购/贸易",
        "subLevelModelList": [
            {
                "code": 1000810,
                "name": "采购",
                "subLevelModelList": [
                    {
                        "code": 250103,
                        "name": "采购专员/助理",
                    },
                    {
                        "code": 250102,
                        "name": "采购经理/主管",
                    },
                    {
                        "code": 250105,
                        "name": "采购工程师",
                    },
                    {
                        "code": 250104,
                        "name": "买手",
                    },
                    {
                        "code": 250108,
                        "name": "供应商质量工程师",
                    },
                    {
                        "code": 250111,
                        "name": "商品专员/助理",
                    },
                    {
                        "code": 140312,
                        "name": "商品经理",
                    },
                    {
                        "code": 250101,
                        "name": "采购总监",
                    },
                    {
                        "code": 250109,
                        "name": "招标专员",
                    },
                    {
                        "code": 250110,
                        "name": "投标专员",
                    }
                ],
            },
            {
                "code": 2600482,
                "name": "外贸销售",
                "subLevelModelList": [
                    {
                        "code": 250203,
                        "name": "外贸业务员",
                    },
                    {
                        "code": 250201,
                        "name": "外贸经理",
                    },
                    {
                        "code": 250205,
                        "name": "海外销售",
                    }
                ],
            },
            {
                "code": 1000820,
                "name": "进出口贸易",
                "subLevelModelList": [
                    {
                        "code": 250204,
                        "name": "贸易跟单",
                    },
                    {
                        "code": 240117,
                        "name": "单证员",
                    },
                    {
                        "code": 240114,
                        "name": "报关/报检员",
                    }
                ],
            },
            {
                "code": 1000830,
                "name": "其他采购/贸易职位",
                "subLevelModelList": [
                    {
                        "code": 250301,
                        "name": "其他采购/贸易类职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 2152200,
        "name": "汽车",
        "subLevelModelList": [
            {
                "code": 2600772,
                "name": "新能源汽车",
                "subLevelModelList": [
                    {
                        "code": 300801,
                        "name": "电池工程师",
                    },
                    {
                        "code": 300802,
                        "name": "电机工程师",
                    }
                ],
            },
            {
                "code": 2600782,
                "name": "汽车研发/制造",
                "subLevelModelList": [
                    {
                        "code": 230108,
                        "name": "汽车项目管理",
                    },
                    {
                        "code": 230109,
                        "name": "汽车质量工程师",
                    },
                    {
                        "code": 230113,
                        "name": "汽车工艺工程师",
                    },
                    {
                        "code": 230101,
                        "name": "汽车设计",
                    },
                    {
                        "code": 300803,
                        "name": "线束设计",
                    },
                    {
                        "code": 230112,
                        "name": "车身工程师",
                    },
                    {
                        "code": 230102,
                        "name": "汽车造型设计",
                    },
                    {
                        "code": 230107,
                        "name": "汽车零部件设计",
                    },
                    {
                        "code": 230106,
                        "name": "汽车电子工程师",
                    },
                    {
                        "code": 101308,
                        "name": "自动驾驶系统工程师",
                    },
                    {
                        "code": 230105,
                        "name": "动力系统工程师",
                    },
                    {
                        "code": 230110,
                        "name": "内外饰设计工程师",
                    },
                    {
                        "code": 230103,
                        "name": "底盘工程师",
                    },
                    {
                        "code": 230111,
                        "name": "总布置工程师",
                    }
                ],
            },
            {
                "code": 2600802,
                "name": "汽车服务",
                "subLevelModelList": [
                    {
                        "code": 230204,
                        "name": "汽车维修",
                    },
                    {
                        "code": 230205,
                        "name": "汽车美容",
                    },
                    {
                        "code": 230213,
                        "name": "洗车工",
                    },
                    {
                        "code": 230209,
                        "name": "汽车改装",
                    },
                    {
                        "code": 230203,
                        "name": "汽车服务顾问",
                    },
                    {
                        "code": 230208,
                        "name": "4S店店长/维修站长",
                    },
                    {
                        "code": 230207,
                        "name": "二手车评估师",
                    },
                    {
                        "code": 230206,
                        "name": "汽车查勘定损",
                    },
                    {
                        "code": 230214,
                        "name": "加油员",
                    }
                ],
            },
            {
                "code": 2600792,
                "name": "汽车销售",
                "subLevelModelList": [
                    {
                        "code": 230201,
                        "name": "汽车销售",
                    },
                    {
                        "code": 230202,
                        "name": "汽车配件销售",
                    }
                ],
            }
        ],
    },
    {
        "code": 1110000,
        "name": "医疗健康",
        "subLevelModelList": [
            {
                "code": 1000750,
                "name": "护士/护理",
                "subLevelModelList": [
                    {
                        "code": 210201,
                        "name": "护士",
                    },
                    {
                        "code": 210503,
                        "name": "导医",
                    },
                    {
                        "code": 210202,
                        "name": "护士长",
                    },
                    {
                        "code": 290111,
                        "name": "护工",
                    }
                ],
            },
            {
                "code": 1000740,
                "name": "医生/医技",
                "subLevelModelList": [
                    {
                        "code": 210309,
                        "name": "外科医生",
                    },
                    {
                        "code": 210306,
                        "name": "内科医生",
                    },
                    {
                        "code": 210313,
                        "name": "皮肤科医生",
                    },
                    {
                        "code": 210311,
                        "name": "妇产科医生",
                    },
                    {
                        "code": 210310,
                        "name": "儿科医生",
                    },
                    {
                        "code": 210312,
                        "name": "眼科医生",
                    },
                    {
                        "code": 210303,
                        "name": "精神心理科医生",
                    },
                    {
                        "code": 210402,
                        "name": "整形医生",
                    },
                    {
                        "code": 210307,
                        "name": "全科医生",
                    },
                    {
                        "code": 210314,
                        "name": "耳鼻喉科医生",
                    },
                    {
                        "code": 210111,
                        "name": "医学检验",
                    },
                    {
                        "code": 210113,
                        "name": "放射科医生",
                    },
                    {
                        "code": 210114,
                        "name": "超声科医生",
                    },
                    {
                        "code": 210315,
                        "name": "麻醉科医生",
                    },
                    {
                        "code": 210316,
                        "name": "病理科医生",
                    },
                    {
                        "code": 210112,
                        "name": "医生助理",
                    },
                    {
                        "code": 210302,
                        "name": "中医",
                    },
                    {
                        "code": 210304,
                        "name": "口腔科医生",
                    },
                    {
                        "code": 210308,
                        "name": "幼儿园保健医",
                    },
                    {
                        "code": 210104,
                        "name": "药剂师",
                    },
                    {
                        "code": 210109,
                        "name": "验光师",
                    },
                    {
                        "code": 210317,
                        "name": "医务管理",
                    },
                    {
                        "code": 210103,
                        "name": "其他医生职位",
                    }
                ],
            },
            {
                "code": 1000760,
                "name": "理疗保健",
                "subLevelModelList": [
                    {
                        "code": 210403,
                        "name": "理疗师",
                    },
                    {
                        "code": 210401,
                        "name": "营养师/健康管理师",
                    },
                    {
                        "code": 210404,
                        "name": "针灸推拿",
                    },
                    {
                        "code": 210305,
                        "name": "康复治疗师",
                    },
                    {
                        "code": 290118,
                        "name": "产后康复师",
                    },
                    {
                        "code": 260112,
                        "name": "心理咨询师",
                    },
                    {
                        "code": 210412,
                        "name": "按摩师",
                    },
                    {
                        "code": 210411,
                        "name": "足疗师",
                    },
                    {
                        "code": 210415,
                        "name": "采耳师",
                    }
                ],
            },
            {
                "code": 1000790,
                "name": "药店",
                "subLevelModelList": [
                    {
                        "code": 210803,
                        "name": "药店店员",
                    },
                    {
                        "code": 210802,
                        "name": "执业药师/驻店药师",
                    },
                    {
                        "code": 210801,
                        "name": "药店店长",
                    }
                ],
            },
            {
                "code": 1000770,
                "name": "生物医药",
                "subLevelModelList": [
                    {
                        "code": 210115,
                        "name": "生物学研究人员",
                    },
                    {
                        "code": 210108,
                        "name": "医药研发",
                    },
                    {
                        "code": 210128,
                        "name": "生物信息工程师",
                    },
                    {
                        "code": 210117,
                        "name": "药品生产",
                    },
                    {
                        "code": 210116,
                        "name": "药品注册",
                    },
                    {
                        "code": 210123,
                        "name": "医药项目经理",
                    },
                    {
                        "code": 210124,
                        "name": "细胞培养员",
                    },
                    {
                        "code": 210130,
                        "name": "药理毒理研究员",
                    },
                    {
                        "code": 210126,
                        "name": "药物合成",
                    },
                    {
                        "code": 210129,
                        "name": "制剂研发",
                    },
                    {
                        "code": 210125,
                        "name": "药物分析",
                    },
                    {
                        "code": 210127,
                        "name": "医疗产品技术支持",
                    }
                ],
            },
            {
                "code": 1000730,
                "name": "临床试验",
                "subLevelModelList": [
                    {
                        "code": 210119,
                        "name": "临床协调员",
                    },
                    {
                        "code": 211002,
                        "name": "临床监查员",
                    },
                    {
                        "code": 211001,
                        "name": "临床项目经理",
                    },
                    {
                        "code": 210120,
                        "name": "临床数据分析",
                    },
                    {
                        "code": 210118,
                        "name": "临床医学经理/专员",
                    },
                    {
                        "code": 210501,
                        "name": "临床医学总监",
                    }
                ],
            },
            {
                "code": 1000780,
                "name": "医疗器械",
                "subLevelModelList": [
                    {
                        "code": 210122,
                        "name": "医疗器械生产/质量管理",
                    },
                    {
                        "code": 210105,
                        "name": "医疗器械研发",
                    },
                    {
                        "code": 210121,
                        "name": "医疗器械注册",
                    },
                    {
                        "code": 210901,
                        "name": "试剂研发",
                    }
                ],
            },
            {
                "code": 2600472,
                "name": "医疗销售",
                "subLevelModelList": [
                    {
                        "code": 210502,
                        "name": "医药代表",
                    },
                    {
                        "code": 210506,
                        "name": "医疗器械销售",
                    },
                    {
                        "code": 210505,
                        "name": "医美咨询",
                    },
                    {
                        "code": 210504,
                        "name": "健康顾问",
                    },
                    {
                        "code": 210507,
                        "name": "口腔咨询师",
                    }
                ],
            },
            {
                "code": 1000800,
                "name": "其他医疗健康职位",
                "subLevelModelList": [
                    {
                        "code": 210101,
                        "name": "医学编辑",
                    },
                    {
                        "code": 210701,
                        "name": "其他医疗健康职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1090000,
        "name": "金融",
        "subLevelModelList": [
            {
                "code": 1000610,
                "name": "银行",
                "subLevelModelList": [
                    {
                        "code": 180402,
                        "name": "柜员",
                    },
                    {
                        "code": 180404,
                        "name": "银行大堂经理",
                    },
                    {
                        "code": 180403,
                        "name": "客户经理",
                    },
                    {
                        "code": 180406,
                        "name": "信贷专员",
                    }
                ],
            },
            {
                "code": 1000590,
                "name": "证券/基金/期货",
                "subLevelModelList": [
                    {
                        "code": 180106,
                        "name": "证券交易员",
                    },
                    {
                        "code": 180802,
                        "name": "卖方分析师",
                    },
                    {
                        "code": 180803,
                        "name": "买方分析师",
                    },
                    {
                        "code": 180806,
                        "name": "投资银行业务",
                    },
                    {
                        "code": 180805,
                        "name": "基金经理",
                    },
                    {
                        "code": 180402,
                        "name": "柜员",
                    },
                    {
                        "code": 180807,
                        "name": "量化研究员",
                    }
                ],
            },
            {
                "code": 1000630,
                "name": "中后台",
                "subLevelModelList": [
                    {
                        "code": 150307,
                        "name": "风控",
                    },
                    {
                        "code": 180204,
                        "name": "合规稽查",
                    },
                    {
                        "code": 180203,
                        "name": "资信评估",
                    },
                    {
                        "code": 180304,
                        "name": "清算",
                    },
                    {
                        "code": 180104,
                        "name": "资产评估",
                    },
                    {
                        "code": 180501,
                        "name": "金融产品经理",
                    },
                    {
                        "code": 180503,
                        "name": "催收员",
                    }
                ],
            },
            {
                "code": 1000600,
                "name": "投融资",
                "subLevelModelList": [
                    {
                        "code": 180101,
                        "name": "投资经理",
                    },
                    {
                        "code": 180118,
                        "name": "投资助理",
                    },
                    {
                        "code": 180103,
                        "name": "行业研究",
                    },
                    {
                        "code": 180115,
                        "name": "融资",
                    },
                    {
                        "code": 180117,
                        "name": "投后管理",
                    },
                    {
                        "code": 180116,
                        "name": "并购",
                    },
                    {
                        "code": 180112,
                        "name": "投资总监/VP",
                    },
                    {
                        "code": 180120,
                        "name": "投资者关系/证券事务代表",
                    },
                    {
                        "code": 180111,
                        "name": "其他投融资职位",
                    }
                ],
            },
            {
                "code": 1000620,
                "name": "保险",
                "subLevelModelList": [
                    {
                        "code": 180703,
                        "name": "保险理赔",
                    },
                    {
                        "code": 180702,
                        "name": "保险精算师",
                    }
                ],
            },
            {
                "code": 2600492,
                "name": "金融销售",
                "subLevelModelList": [
                    {
                        "code": 180506,
                        "name": "理财顾问",
                    },
                    {
                        "code": 180701,
                        "name": "保险顾问",
                    },
                    {
                        "code": 180401,
                        "name": "信用卡销售",
                    },
                    {
                        "code": 180801,
                        "name": "证券经纪人",
                    }
                ],
            },
            {
                "code": 1000650,
                "name": "其他金融职位",
                "subLevelModelList": [
                    {
                        "code": 180601,
                        "name": "其他金融职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1220000,
        "name": "项目管理",
        "subLevelModelList": [
            {
                "code": 2600982,
                "name": "项目管理",
                "subLevelModelList": [
                    {
                        "code": 100601,
                        "name": "项目经理/主管",
                    },
                    {
                        "code": 100603,
                        "name": "项目专员/助理",
                    },
                    {
                        "code": 220212,
                        "name": "建筑施工项目经理",
                    },
                    {
                        "code": 220222,
                        "name": "装修项目经理",
                    },
                    {
                        "code": 100608,
                        "name": "软件项目经理",
                    },
                    {
                        "code": 220102,
                        "name": "房地产项目管理",
                    },
                    {
                        "code": 101012,
                        "name": "通信项目经理",
                    },
                    {
                        "code": 100817,
                        "name": "硬件项目经理",
                    },
                    {
                        "code": 260106,
                        "name": "咨询项目管理",
                    },
                    {
                        "code": 240109,
                        "name": "物流/仓储项目经理",
                    },
                    {
                        "code": 170212,
                        "name": "广告/会展项目经理",
                    },
                    {
                        "code": 230108,
                        "name": "汽车项目管理",
                    },
                    {
                        "code": 300322,
                        "name": "生产制造项目经理",
                    },
                    {
                        "code": 300407,
                        "name": "化工项目经理",
                    },
                    {
                        "code": 211001,
                        "name": "临床项目经理",
                    },
                    {
                        "code": 210123,
                        "name": "医药项目经理",
                    }
                ],
            }
        ],
    },
    {
        "code": 1160000,
        "name": "咨询/翻译/法律",
        "subLevelModelList": [
            {
                "code": 1000960,
                "name": "咨询/调研",
                "subLevelModelList": [
                    {
                        "code": 260111,
                        "name": "知识产权/专利/商标代理人",
                    },
                    {
                        "code": 140804,
                        "name": "项目申报专员",
                    },
                    {
                        "code": 260106,
                        "name": "咨询项目管理",
                    },
                    {
                        "code": 260101,
                        "name": "企业管理咨询",
                    },
                    {
                        "code": 260107,
                        "name": "战略咨询",
                    },
                    {
                        "code": 150303,
                        "name": "财务顾问",
                    },
                    {
                        "code": 260104,
                        "name": "IT咨询顾问",
                    },
                    {
                        "code": 260402,
                        "name": "咨询经理",
                    },
                    {
                        "code": 260105,
                        "name": "人力资源咨询顾问",
                    },
                    {
                        "code": 260108,
                        "name": "猎头顾问",
                    },
                    {
                        "code": 260401,
                        "name": "咨询总监",
                    },
                    {
                        "code": 260109,
                        "name": "市场调研",
                    },
                    {
                        "code": 100511,
                        "name": "数据分析师",
                    },
                    {
                        "code": 260112,
                        "name": "心理咨询师",
                    },
                    {
                        "code": 260113,
                        "name": "婚恋咨询师",
                    },
                    {
                        "code": 260114,
                        "name": "工程咨询",
                    },
                    {
                        "code": 260110,
                        "name": "其他咨询顾问",
                    }
                ],
            },
            {
                "code": 1000980,
                "name": "翻译",
                "subLevelModelList": [
                    {
                        "code": 260301,
                        "name": "英语翻译",
                    },
                    {
                        "code": 260302,
                        "name": "日语翻译",
                    },
                    {
                        "code": 260303,
                        "name": "韩语/朝鲜语翻译",
                    },
                    {
                        "code": 260306,
                        "name": "俄语翻译",
                    },
                    {
                        "code": 260307,
                        "name": "西班牙语翻译",
                    },
                    {
                        "code": 260305,
                        "name": "德语翻译",
                    },
                    {
                        "code": 260304,
                        "name": "法语翻译",
                    },
                    {
                        "code": 260308,
                        "name": "其他语种翻译",
                    }
                ],
            },
            {
                "code": 1000970,
                "name": "法律服务",
                "subLevelModelList": [
                    {
                        "code": 260204,
                        "name": "律师助理",
                    },
                    {
                        "code": 260203,
                        "name": "知识产权律师",
                    },
                    {
                        "code": 260201,
                        "name": "律师",
                    }
                ],
            },
            {
                "code": 1000990,
                "name": "其他咨询类职位",
                "subLevelModelList": [
                    {
                        "code": 260501,
                        "name": "其他咨询/翻译类职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1150000,
        "name": "能源/环保/农业",
        "subLevelModelList": [
            {
                "code": 1000940,
                "name": "能源/地质",
                "subLevelModelList": [
                    {
                        "code": 301002,
                        "name": "光伏系统工程师",
                    },
                    {
                        "code": 301003,
                        "name": "风电/光伏运维工程师",
                    },
                    {
                        "code": 301004,
                        "name": "水利工程师",
                    },
                    {
                        "code": 301001,
                        "name": "地质工程师",
                    },
                    {
                        "code": 101414,
                        "name": "电力工程师",
                    }
                ],
            },
            {
                "code": 2600612,
                "name": "环保",
                "subLevelModelList": [
                    {
                        "code": 300903,
                        "name": "EHS工程师",
                    },
                    {
                        "code": 300905,
                        "name": "环境采样/检测员",
                    },
                    {
                        "code": 300902,
                        "name": "环评工程师",
                    },
                    {
                        "code": 300901,
                        "name": "环保工程师",
                    },
                    {
                        "code": 300904,
                        "name": "碳排放管理师",
                    }
                ],
            },
            {
                "code": 1000950,
                "name": "农/林/牧/渔",
                "subLevelModelList": [
                    {
                        "code": 400101,
                        "name": "农业/林业技术员",
                    },
                    {
                        "code": 400201,
                        "name": "饲养员",
                    },
                    {
                        "code": 400202,
                        "name": "养殖技术员",
                    },
                    {
                        "code": 400203,
                        "name": "畜牧兽医",
                    }
                ],
            }
        ],
    },
    {
        "code": 1000000,
        "name": "高级管理",
        "subLevelModelList": [
            {
                "code": 1000010,
                "name": "高级管理职位",
                "subLevelModelList": [
                    {
                        "code": 150407,
                        "name": "总裁/总经理/CEO",
                    },
                    {
                        "code": 150408,
                        "name": "副总裁/副总经理/VP",
                    },
                    {
                        "code": 150411,
                        "name": "总助/CEO助理/董事长助理",
                    },
                    {
                        "code": 150410,
                        "name": "区域负责人(辖多个分公司)",
                    },
                    {
                        "code": 150409,
                        "name": "分公司/代表处负责人",
                    },
                    {
                        "code": 150414,
                        "name": "董事会秘书",
                    },
                    {
                        "code": 150413,
                        "name": "联合创始人",
                    },
                    {
                        "code": 150499,
                        "name": "高级管理职位",
                    }
                ],
            }
        ],
    },
    {
        "code": 1200000,
        "name": "其他",
        "subLevelModelList": [
            {
                "code": 1001220,
                "name": "其他职位类别",
                "subLevelModelList": [
                    {
                        "code": 200101,
                        "name": "其他职位",
                    }
                ],
            }
        ],
    }
]

hot_city = [{'name': '全国', 'code': 100010000}, {'name': '北京', 'code': 101010100}, {'name': '上海', 'code': 101020100},
            {'name': '广州', 'code': 101280100}, {'name': '深圳', 'code': 101280600}, {'name': '杭州', 'code': 101210100},
            {'name': '天津', 'code': 101030100}, {'name': '西安', 'code': 101110100}, {'name': '苏州', 'code': 101190400},
            {'name': '武汉', 'code': 101200100}, {'name': '厦门', 'code': 101230200}, {'name': '长沙', 'code': 101250100},
            {'name': '成都', 'code': 101270100}, {'name': '郑州', 'code': 101180100}, {'name': '重庆', 'code': 101040100},
            {'name': '佛山', 'code': 101280800}, {'name': '合肥', 'code': 101220100}, {'name': '济南', 'code': 101120100},
            {'name': '青岛', 'code': 101120200}, {'name': '南京', 'code': 101190100}, {'name': '东莞', 'code': 101281600},
            {'name': '昆明', 'code': 101290100}, {'name': '南昌', 'code': 101240100}, {'name': '石家庄', 'code': 101090100},
            {'name': '宁波', 'code': 101210400}, {'name': '福州', 'code': 101230100}]

all_city = [
    {
        "code": 101010000,
        "name": "北京",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101010100,
                "name": "北京",
            }
        ]
    },
    {
        "code": 101030000,
        "name": "天津",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101030100,
                "name": "天津",

            }
        ]
    },
    {
        "code": 101090000,
        "name": "河北",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101090100,
                "name": "石家庄",

            },
            {
                "code": 101090500,
                "name": "唐山",

            },
            {
                "code": 101091100,
                "name": "秦皇岛",

            },
            {
                "code": 101091000,
                "name": "邯郸",
            },
            {
                "code": 101090900,
                "name": "邢台",

            },
            {
                "code": 101090200,
                "name": "保定",

            },
            {
                "code": 101090300,
                "name": "张家口",

            },
            {
                "code": 101090400,
                "name": "承德",

            },
            {
                "code": 101090700,
                "name": "沧州",

            },
            {
                "code": 101090600,
                "name": "廊坊",

            },
            {
                "code": 101090800,
                "name": "衡水",

            }
        ]
    },
    {
        "code": 101100000,
        "name": "山西",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101100100,
                "name": "太原",

            },
            {
                "code": 101100200,
                "name": "大同",

            },
            {
                "code": 101100300,
                "name": "阳泉",

            },
            {
                "code": 101100500,
                "name": "长治",

            },
            {
                "code": 101100600,
                "name": "晋城",

            },
            {
                "code": 101100900,
                "name": "朔州",

            },
            {
                "code": 101100400,
                "name": "晋中",

            },
            {
                "code": 101100800,
                "name": "运城",

            },
            {
                "code": 101101000,
                "name": "忻州",

            },
            {
                "code": 101100700,
                "name": "临汾",

            },
            {
                "code": 101101100,
                "name": "吕梁",

            }
        ]
    },
    {
        "code": 101080000,
        "name": "内蒙古",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101080100,
                "name": "呼和浩特",

            },
            {
                "code": 101080200,
                "name": "包头",

            },
            {
                "code": 101080300,
                "name": "乌海",

            },
            {
                "code": 101080500,
                "name": "赤峰",

            },
            {
                "code": 101080400,
                "name": "通辽",

            },
            {
                "code": 101080600,
                "name": "鄂尔多斯",

            },
            {
                "code": 101080700,
                "name": "呼伦贝尔",

            },
            {
                "code": 101080800,
                "name": "巴彦淖尔",

            },
            {
                "code": 101080900,
                "name": "乌兰察布",

            },
            {
                "code": 101081100,
                "name": "兴安盟",

            },
            {
                "code": 101081000,
                "name": "锡林郭勒盟",

            },
            {
                "code": 101081200,
                "name": "阿拉善盟",

            }
        ]
    },
    {
        "code": 101070000,
        "name": "辽宁",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101070100,
                "name": "沈阳",

            },
            {
                "code": 101070200,
                "name": "大连",

            },
            {
                "code": 101070300,
                "name": "鞍山",

            },
            {
                "code": 101070400,
                "name": "抚顺",

            },
            {
                "code": 101070500,
                "name": "本溪",

            },
            {
                "code": 101070600,
                "name": "丹东",

            },
            {
                "code": 101070700,
                "name": "锦州",

            },
            {
                "code": 101070800,
                "name": "营口",

            },
            {
                "code": 101070900,
                "name": "阜新",

            },
            {
                "code": 101071000,
                "name": "辽阳",

            },
            {
                "code": 101071300,
                "name": "盘锦",

            },
            {
                "code": 101071100,
                "name": "铁岭",

            },
            {
                "code": 101071200,
                "name": "朝阳",

            },
            {
                "code": 101071400,
                "name": "葫芦岛",

            }
        ]
    },
    {
        "code": 101060000,
        "name": "吉林",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101060100,
                "name": "长春",

            },
            {
                "code": 101060200,
                "name": "吉林",

            },
            {
                "code": 101060300,
                "name": "四平",

            },
            {
                "code": 101060600,
                "name": "辽源",

            },
            {
                "code": 101060400,
                "name": "通化",

            },
            {
                "code": 101060800,
                "name": "白山",

            },
            {
                "code": 101060700,
                "name": "松原",

            },
            {
                "code": 101060500,
                "name": "白城",

            },
            {
                "code": 101060900,
                "name": "延边朝鲜族自治州",

            }
        ]
    },
    {
        "code": 101050000,
        "name": "黑龙江",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101050100,
                "name": "哈尔滨",

            },
            {
                "code": 101050200,
                "name": "齐齐哈尔",

            },
            {
                "code": 101051000,
                "name": "鸡西",

            },
            {
                "code": 101051100,
                "name": "鹤岗",

            },
            {
                "code": 101051200,
                "name": "双鸭山",

            },
            {
                "code": 101050800,
                "name": "大庆",

            },
            {
                "code": 101050700,
                "name": "伊春",

            },
            {
                "code": 101050400,
                "name": "佳木斯",

            },
            {
                "code": 101050900,
                "name": "七台河",

            },
            {
                "code": 101050300,
                "name": "牡丹江",

            },
            {
                "code": 101050600,
                "name": "黑河",

            },
            {
                "code": 101050500,
                "name": "绥化",

            },
            {
                "code": 101051300,
                "name": "大兴安岭地区",

            }
        ]
    },
    {
        "code": 101020000,
        "name": "上海",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101020100,
                "name": "上海",

            }
        ]
    },
    {
        "code": 101190000,
        "name": "江苏",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101190100,
                "name": "南京",

            },
            {
                "code": 101190200,
                "name": "无锡",

            },
            {
                "code": 101190800,
                "name": "徐州",

            },
            {
                "code": 101190400,
                "name": "苏州",

            },
            {
                "code": 101191100,
                "name": "常州",

            },
            {
                "code": 101190500,
                "name": "南通",

            },
            {
                "code": 101191000,
                "name": "连云港",

            },
            {
                "code": 101190900,
                "name": "淮安",

            },
            {
                "code": 101190700,
                "name": "盐城",

            },
            {
                "code": 101190600,
                "name": "扬州",

            },
            {
                "code": 101190300,
                "name": "镇江",

            },
            {
                "code": 101191200,
                "name": "泰州",

            },
            {
                "code": 101191300,
                "name": "宿迁",

            }
        ]
    },
    {
        "code": 101210000,
        "name": "浙江",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101210100,
                "name": "杭州",

            },
            {
                "code": 101210400,
                "name": "宁波",

            },
            {
                "code": 101210700,
                "name": "温州",

            },
            {
                "code": 101210300,
                "name": "嘉兴",

            },
            {
                "code": 101210200,
                "name": "湖州",

            },
            {
                "code": 101210500,
                "name": "绍兴",

            },
            {
                "code": 101210900,
                "name": "金华",

            },
            {
                "code": 101211000,
                "name": "衢州",

            },
            {
                "code": 101211100,
                "name": "舟山",

            },
            {
                "code": 101210600,
                "name": "台州",

            },
            {
                "code": 101210800,
                "name": "丽水",

            }
        ]
    },
    {
        "code": 101220000,
        "name": "安徽",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101220100,
                "name": "合肥",

            },
            {
                "code": 101220300,
                "name": "芜湖",

            },
            {
                "code": 101220200,
                "name": "蚌埠",

            },
            {
                "code": 101220400,
                "name": "淮南",

            },
            {
                "code": 101220500,
                "name": "马鞍山",

            },
            {
                "code": 101221100,
                "name": "淮北",

            },
            {
                "code": 101221200,
                "name": "铜陵",

            },
            {
                "code": 101220600,
                "name": "安庆",

            },
            {
                "code": 101221600,
                "name": "黄山",

            },
            {
                "code": 101221000,
                "name": "滁州",

            },
            {
                "code": 101220800,
                "name": "阜阳",

            },
            {
                "code": 101220700,
                "name": "宿州",

            },
            {
                "code": 101221400,
                "name": "六安",

            },
            {
                "code": 101220900,
                "name": "亳州",

            },
            {
                "code": 101221500,
                "name": "池州",

            },
            {
                "code": 101221300,
                "name": "宣城",

            }
        ]
    },
    {
        "code": 101230000,
        "name": "福建",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101230100,
                "name": "福州",

            },
            {
                "code": 101230200,
                "name": "厦门",

            },
            {
                "code": 101230400,
                "name": "莆田",

            },
            {
                "code": 101230800,
                "name": "三明",

            },
            {
                "code": 101230500,
                "name": "泉州",

            },
            {
                "code": 101230600,
                "name": "漳州",

            },
            {
                "code": 101230900,
                "name": "南平",

            },
            {
                "code": 101230700,
                "name": "龙岩",

            },
            {
                "code": 101230300,
                "name": "宁德",

            }
        ]
    },
    {
        "code": 101240000,
        "name": "江西",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101240100,
                "name": "南昌",

            },
            {
                "code": 101240800,
                "name": "景德镇",

            },
            {
                "code": 101240900,
                "name": "萍乡",

            },
            {
                "code": 101240200,
                "name": "九江",

            },
            {
                "code": 101241000,
                "name": "新余",

            },
            {
                "code": 101241100,
                "name": "鹰潭",

            },
            {
                "code": 101240700,
                "name": "赣州",

            },
            {
                "code": 101240600,
                "name": "吉安",

            },
            {
                "code": 101240500,
                "name": "宜春",

            },
            {
                "code": 101240400,
                "name": "抚州",

            },
            {
                "code": 101240300,
                "name": "上饶",

            }
        ]
    },
    {
        "code": 101120000,
        "name": "山东",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101120100,
                "name": "济南",

            },
            {
                "code": 101120200,
                "name": "青岛",

            },
            {
                "code": 101120300,
                "name": "淄博",

            },
            {
                "code": 101121400,
                "name": "枣庄",

            },
            {
                "code": 101121200,
                "name": "东营",

            },
            {
                "code": 101120500,
                "name": "烟台",

            },
            {
                "code": 101120600,
                "name": "潍坊",

            },
            {
                "code": 101120700,
                "name": "济宁",

            },
            {
                "code": 101120800,
                "name": "泰安",

            },
            {
                "code": 101121300,
                "name": "威海",

            },
            {
                "code": 101121500,
                "name": "日照",

            },
            {
                "code": 101120900,
                "name": "临沂",

            },
            {
                "code": 101120400,
                "name": "德州",

            },
            {
                "code": 101121700,
                "name": "聊城",

            },
            {
                "code": 101121100,
                "name": "滨州",

            },
            {
                "code": 101121000,
                "name": "菏泽",

            }
        ]
    },
    {
        "code": 101180000,
        "name": "河南",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101180100,
                "name": "郑州",

            },
            {
                "code": 101180800,
                "name": "开封",

            },
            {
                "code": 101180900,
                "name": "洛阳",

            },
            {
                "code": 101180500,
                "name": "平顶山",

            },
            {
                "code": 101180200,
                "name": "安阳",

            },
            {
                "code": 101181200,
                "name": "鹤壁",

            },
            {
                "code": 101180300,
                "name": "新乡",

            },
            {
                "code": 101181100,
                "name": "焦作",

            },
            {
                "code": 101181300,
                "name": "濮阳",

            },
            {
                "code": 101180400,
                "name": "许昌",

            },
            {
                "code": 101181500,
                "name": "漯河",

            },
            {
                "code": 101181700,
                "name": "三门峡",

            },
            {
                "code": 101180700,
                "name": "南阳",

            },
            {
                "code": 101181000,
                "name": "商丘",

            },
            {
                "code": 101180600,
                "name": "信阳",

            },
            {
                "code": 101181400,
                "name": "周口",

            },
            {
                "code": 101181600,
                "name": "驻马店",

            },
            {
                "code": 101181800,
                "name": "济源",

            }
        ]
    },
    {
        "code": 101200000,
        "name": "湖北",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101200100,
                "name": "武汉",

            },
            {
                "code": 101200600,
                "name": "黄石",

            },
            {
                "code": 101201000,
                "name": "十堰",

            },
            {
                "code": 101200900,
                "name": "宜昌",

            },
            {
                "code": 101200200,
                "name": "襄阳",

            },
            {
                "code": 101200300,
                "name": "鄂州",

            },
            {
                "code": 101201200,
                "name": "荆门",

            },
            {
                "code": 101200400,
                "name": "孝感",

            },
            {
                "code": 101200800,
                "name": "荆州",

            },
            {
                "code": 101200500,
                "name": "黄冈",

            },
            {
                "code": 101200700,
                "name": "咸宁",

            },
            {
                "code": 101201100,
                "name": "随州",

            },
            {
                "code": 101201300,
                "name": "恩施土家族苗族自治州",

            },
            {
                "code": 101201500,
                "name": "潜江",

            },
            {
                "code": 101201700,
                "name": "神农架",

            },
            {
                "code": 101201600,
                "name": "天门",

            },
            {
                "code": 101201400,
                "name": "仙桃",

            }
        ]
    },
    {
        "code": 101250000,
        "name": "湖南",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101250100,
                "name": "长沙",

            },
            {
                "code": 101250300,
                "name": "株洲",

            },
            {
                "code": 101250200,
                "name": "湘潭",

            },
            {
                "code": 101250400,
                "name": "衡阳",

            },
            {
                "code": 101250900,
                "name": "邵阳",

            },
            {
                "code": 101251000,
                "name": "岳阳",

            },
            {
                "code": 101250600,
                "name": "常德",

            },
            {
                "code": 101251100,
                "name": "张家界",

            },
            {
                "code": 101250700,
                "name": "益阳",

            },
            {
                "code": 101250500,
                "name": "郴州",

            },
            {
                "code": 101251300,
                "name": "永州",

            },
            {
                "code": 101251200,
                "name": "怀化",

            },
            {
                "code": 101250800,
                "name": "娄底",

            },
            {
                "code": 101251400,
                "name": "湘西土家族苗族自治州",

            }
        ]
    },
    {
        "code": 101280000,
        "name": "广东",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101280100,
                "name": "广州",

            },
            {
                "code": 101280200,
                "name": "韶关",

            },
            {
                "code": 101280600,
                "name": "深圳",

            },
            {
                "code": 101280700,
                "name": "珠海",

            },
            {
                "code": 101280500,
                "name": "汕头",

            },
            {
                "code": 101280800,
                "name": "佛山",

            },
            {
                "code": 101281100,
                "name": "江门",

            },
            {
                "code": 101281000,
                "name": "湛江",

            },
            {
                "code": 101282000,
                "name": "茂名",

            },
            {
                "code": 101280900,
                "name": "肇庆",

            },
            {
                "code": 101280300,
                "name": "惠州",

            },
            {
                "code": 101280400,
                "name": "梅州",

            },
            {
                "code": 101282100,
                "name": "汕尾",

            },
            {
                "code": 101281200,
                "name": "河源",

            },
            {
                "code": 101281800,
                "name": "阳江",

            },
            {
                "code": 101281300,
                "name": "清远",

            },
            {
                "code": 101281600,
                "name": "东莞",

            },
            {
                "code": 101281700,
                "name": "中山",

            },
            {
                "code": 101281500,
                "name": "潮州",

            },
            {
                "code": 101281900,
                "name": "揭阳",

            },
            {
                "code": 101281400,
                "name": "云浮",

            },
            {
                "code": 101282200,
                "name": "东沙群岛",

            }
        ]
    },
    {
        "code": 101300000,
        "name": "广西",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101300100,
                "name": "南宁",

            },
            {
                "code": 101300300,
                "name": "柳州",

            },
            {
                "code": 101300500,
                "name": "桂林",

            },
            {
                "code": 101300600,
                "name": "梧州",

            },
            {
                "code": 101301300,
                "name": "北海",

            },
            {
                "code": 101301400,
                "name": "防城港",

            },
            {
                "code": 101301100,
                "name": "钦州",

            },
            {
                "code": 101300800,
                "name": "贵港",

            },
            {
                "code": 101300900,
                "name": "玉林",

            },
            {
                "code": 101301000,
                "name": "百色",

            },
            {
                "code": 101300700,
                "name": "贺州",

            },
            {
                "code": 101301200,
                "name": "河池",

            },
            {
                "code": 101300400,
                "name": "来宾",

            },
            {
                "code": 101300200,
                "name": "崇左",

            }
        ]
    },
    {
        "code": 101310000,
        "name": "海南",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101311400,
                "name": "白沙黎族自治县",

            },
            {
                "code": 101311800,
                "name": "保亭黎族苗族自治县",

            },
            {
                "code": 101311500,
                "name": "昌江黎族自治县",

            },
            {
                "code": 101311200,
                "name": "澄迈",

            },
            {
                "code": 101310100,
                "name": "海口",

            },
            {
                "code": 101310200,
                "name": "三亚",

            },
            {
                "code": 101310300,
                "name": "三沙",

            },
            {
                "code": 101310400,
                "name": "儋州",

            },
            {
                "code": 101311000,
                "name": "定安",

            },
            {
                "code": 101310900,
                "name": "东方",

            },
            {
                "code": 101311600,
                "name": "乐东黎族自治县",

            },
            {
                "code": 101311300,
                "name": "临高",

            },
            {
                "code": 101311700,
                "name": "陵水黎族自治县",

            },
            {
                "code": 101310600,
                "name": "琼海",

            },
            {
                "code": 101311900,
                "name": "琼中黎族苗族自治县",

            },
            {
                "code": 101311100,
                "name": "屯昌",

            },
            {
                "code": 101310800,
                "name": "万宁",

            },
            {
                "code": 101310700,
                "name": "文昌",

            },
            {
                "code": 101310500,
                "name": "五指山",

            }
        ]
    },
    {
        "code": 101040000,
        "name": "重庆",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101040100,
                "name": "重庆",

            }
        ]
    },
    {
        "code": 101270000,
        "name": "四川",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101270100,
                "name": "成都",

            },
            {
                "code": 101270300,
                "name": "自贡",

            },
            {
                "code": 101270200,
                "name": "攀枝花",

            },
            {
                "code": 101271000,
                "name": "泸州",

            },
            {
                "code": 101271700,
                "name": "德阳",

            },
            {
                "code": 101270400,
                "name": "绵阳",

            },
            {
                "code": 101271800,
                "name": "广元",

            },
            {
                "code": 101270700,
                "name": "遂宁",

            },
            {
                "code": 101271200,
                "name": "内江",

            },
            {
                "code": 101271400,
                "name": "乐山",

            },
            {
                "code": 101270500,
                "name": "南充",

            },
            {
                "code": 101271500,
                "name": "眉山",

            },
            {
                "code": 101271100,
                "name": "宜宾",

            },
            {
                "code": 101270800,
                "name": "广安",

            },
            {
                "code": 101270600,
                "name": "达州",

            },
            {
                "code": 101271600,
                "name": "雅安",

            },
            {
                "code": 101270900,
                "name": "巴中",

            },
            {
                "code": 101271300,
                "name": "资阳",

            },
            {
                "code": 101271900,
                "name": "阿坝藏族羌族自治州",

            },
            {
                "code": 101272100,
                "name": "甘孜藏族自治州",

            },
            {
                "code": 101272000,
                "name": "凉山彝族自治州",

            }
        ]
    },
    {
        "code": 101260000,
        "name": "贵州",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101260100,
                "name": "贵阳",

            },
            {
                "code": 101260600,
                "name": "六盘水",

            },
            {
                "code": 101260200,
                "name": "遵义",

            },
            {
                "code": 101260300,
                "name": "安顺",

            },
            {
                "code": 101260500,
                "name": "毕节",

            },
            {
                "code": 101260400,
                "name": "铜仁",

            },
            {
                "code": 101260900,
                "name": "黔西南布依族苗族自治州",

            },
            {
                "code": 101260700,
                "name": "黔东南苗族侗族自治州",

            },
            {
                "code": 101260800,
                "name": "黔南布依族苗族自治州",

            }
        ]
    },
    {
        "code": 101290000,
        "name": "云南",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101290100,
                "name": "昆明",

            },
            {
                "code": 101290200,
                "name": "曲靖",

            },
            {
                "code": 101290400,
                "name": "玉溪",

            },
            {
                "code": 101290300,
                "name": "保山",

            },
            {
                "code": 101290700,
                "name": "昭通",

            },
            {
                "code": 101290900,
                "name": "丽江",

            },
            {
                "code": 101290500,
                "name": "普洱",

            },
            {
                "code": 101290800,
                "name": "临沧",

            },
            {
                "code": 101291700,
                "name": "楚雄彝族自治州",

            },
            {
                "code": 101291200,
                "name": "红河哈尼族彝族自治州",

            },
            {
                "code": 101291100,
                "name": "文山壮族苗族自治州",

            },
            {
                "code": 101291000,
                "name": "西双版纳傣族自治州",

            },
            {
                "code": 101291600,
                "name": "大理白族自治州",

            },
            {
                "code": 101291300,
                "name": "德宏傣族景颇族自治州",

            },
            {
                "code": 101291400,
                "name": "怒江傈僳族自治州",

            },
            {
                "code": 101291500,
                "name": "迪庆藏族自治州",

            }
        ]
    },
    {
        "code": 101140000,
        "name": "西藏",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101140100,
                "name": "拉萨",

            },
            {
                "code": 101140200,
                "name": "日喀则",

            },
            {
                "code": 101140300,
                "name": "昌都",

            },
            {
                "code": 101140400,
                "name": "林芝",

            },
            {
                "code": 101140500,
                "name": "山南",

            },
            {
                "code": 101140600,
                "name": "那曲",

            },
            {
                "code": 101140700,
                "name": "阿里地区",

            }
        ]
    },
    {
        "code": 101110000,
        "name": "陕西",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101110100,
                "name": "西安",

            },
            {
                "code": 101111000,
                "name": "铜川",

            },
            {
                "code": 101110900,
                "name": "宝鸡",

            },
            {
                "code": 101110200,
                "name": "咸阳",

            },
            {
                "code": 101110500,
                "name": "渭南",

            },
            {
                "code": 101110300,
                "name": "延安",

            },
            {
                "code": 101110800,
                "name": "汉中",

            },
            {
                "code": 101110400,
                "name": "榆林",

            },
            {
                "code": 101110700,
                "name": "安康",

            },
            {
                "code": 101110600,
                "name": "商洛",

            }
        ]
    },
    {
        "code": 101160000,
        "name": "甘肃",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101160100,
                "name": "兰州",

            },
            {
                "code": 101161200,
                "name": "嘉峪关",

            },
            {
                "code": 101160600,
                "name": "金昌",

            },
            {
                "code": 101161000,
                "name": "白银",

            },
            {
                "code": 101160900,
                "name": "天水",

            },
            {
                "code": 101160500,
                "name": "武威",

            },
            {
                "code": 101160700,
                "name": "张掖",

            },
            {
                "code": 101160300,
                "name": "平凉",

            },
            {
                "code": 101160800,
                "name": "酒泉",

            },
            {
                "code": 101160400,
                "name": "庆阳",

            },
            {
                "code": 101160200,
                "name": "定西",

            },
            {
                "code": 101161100,
                "name": "陇南",

            },
            {
                "code": 101161300,
                "name": "临夏回族自治州",

            },
            {
                "code": 101161400,
                "name": "甘南藏族自治州",

            }
        ]
    },
    {
        "code": 101150000,
        "name": "青海",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101150100,
                "name": "西宁",

            },
            {
                "code": 101150200,
                "name": "海东",

            },
            {
                "code": 101150300,
                "name": "海北藏族自治州",

            },
            {
                "code": 101150400,
                "name": "黄南藏族自治州",

            },
            {
                "code": 101150500,
                "name": "海南藏族自治州",

            },
            {
                "code": 101150600,
                "name": "果洛藏族自治州",

            },
            {
                "code": 101150700,
                "name": "玉树藏族自治州",

            },
            {
                "code": 101150800,
                "name": "海西蒙古族藏族自治州",

            }
        ]
    },
    {
        "code": 101170000,
        "name": "宁夏",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101170100,
                "name": "银川",

            },
            {
                "code": 101170200,
                "name": "石嘴山",

            },
            {
                "code": 101170300,
                "name": "吴忠",

            },
            {
                "code": 101170400,
                "name": "固原",

            },
            {
                "code": 101170500,
                "name": "中卫",

            }
        ]
    },
    {
        "code": 101130000,
        "name": "新疆",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101132500,
                "name": "新星市",

            },
            {
                "code": 101132600,
                "name": "胡杨河市",

            },
            {
                "code": 101132700,
                "name": "白杨市",

            },
            {
                "code": 101131700,
                "name": "阿拉尔",

            },
            {
                "code": 101132100,
                "name": "北屯市",

            },
            {
                "code": 101132200,
                "name": "可克达拉市",

            },
            {
                "code": 101132300,
                "name": "昆玉市",

            },
            {
                "code": 101131600,
                "name": "石河子",

            },
            {
                "code": 101132400,
                "name": "双河市",

            },
            {
                "code": 101130100,
                "name": "乌鲁木齐",

            },
            {
                "code": 101130200,
                "name": "克拉玛依",

            },
            {
                "code": 101130800,
                "name": "吐鲁番",

            },
            {
                "code": 101130900,
                "name": "哈密",

            },
            {
                "code": 101130300,
                "name": "昌吉回族自治州",

            },
            {
                "code": 101130500,
                "name": "博尔塔拉蒙古自治州",

            },
            {
                "code": 101130400,
                "name": "巴音郭楞蒙古自治州",

            },
            {
                "code": 101131000,
                "name": "阿克苏地区",

            },
            {
                "code": 101131100,
                "name": "克孜勒苏柯尔克孜自治州",

            },
            {
                "code": 101131200,
                "name": "喀什地区",

            },
            {
                "code": 101131300,
                "name": "和田地区",

            },
            {
                "code": 101130600,
                "name": "伊犁哈萨克自治州",

            },
            {
                "code": 101131400,
                "name": "塔城地区",

            },
            {
                "code": 101131500,
                "name": "阿勒泰地区",

            },
            {
                "code": 101132000,
                "name": "铁门关",

            },
            {
                "code": 101131800,
                "name": "图木舒克",

            },
            {
                "code": 101131900,
                "name": "五家渠",

            }
        ]
    },
    {
        "code": 101340000,
        "name": "台湾",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101341100,
                "name": "台湾",

            }
        ]
    },
    {
        "code": 101320000,
        "name": "香港",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101320300,
                "name": "香港",

            }
        ]
    },
    {
        "code": 101330000,
        "name": "澳门",
        "url": "",
        "subLevelModelList": [
            {
                "code": 101330100,
                "name": "澳门",

            }
        ]
    }
]

conditions = {
    'experienceList': [{'code': 0, 'name': '不限'}, {'code': 108, 'name': '在校生'}, {'code': 102, 'name': '应届生'},
                       {'code': 101, 'name': '经验不限'}, {'code': 103, 'name': '1年以内'}, {'code': 104, 'name': '1-3年'},
                       {'code': 105, 'name': '3-5年'}, {'code': 106, 'name': '5-10年'}, {'code': 107, 'name': '10年以上'}],
    'salaryList': [{'code': 0, 'name': '不限', 'lowSalary': 0, 'highSalary': 0},
                   {'code': 402, 'name': '3K以下', 'lowSalary': 0, 'highSalary': 3},
                   {'code': 403, 'name': '3-5K', 'lowSalary': 3, 'highSalary': 5},
                   {'code': 404, 'name': '5-10K', 'lowSalary': 5, 'highSalary': 10},
                   {'code': 405, 'name': '10-20K', 'lowSalary': 10, 'highSalary': 20},
                   {'code': 406, 'name': '20-50K', 'lowSalary': 20, 'highSalary': 50},
                   {'code': 407, 'name': '50K以上', 'lowSalary': 50, 'highSalary': 0}],
    'stageList': [{'code': 0, 'name': '不限'}, {'code': 801, 'name': '未融资'}, {'code': 802, 'name': '天使轮'},
                  {'code': 803, 'name': 'A轮'}, {'code': 804, 'name': 'B轮'}, {'code': 805, 'name': 'C轮'},
                  {'code': 806, 'name': 'D轮及以上'}, {'code': 807, 'name': '已上市'}, {'code': 808, 'name': '不需要融资'}],
    'scaleList': [{'code': 0, 'name': '不限'}, {'code': 301, 'name': '0-20人'}, {'code': 302, 'name': '20-99人'},
                  {'code': 303, 'name': '100-499人'}, {'code': 304, 'name': '500-999人'}, {'code': 305, 'name': '1000-9999人'},
                  {'code': 306, 'name': '10000人以上'}],
    'degreeList': [{'code': 0, 'name': '不限'}, {'code': 209, 'name': '初中及以下'}, {'code': 208, 'name': '中专/中技'},
                   {'code': 206, 'name': '高中'}, {'code': 202, 'name': '大专'}, {'code': 203, 'name': '本科'},
                   {'code': 204, 'name': '硕士'}, {'code': 205, 'name': '博士'}],
    'jobTypeList': [{'code': 0, 'name': '不限'}, {'code': 1901, 'name': '全职'}, {'code': 1903, 'name': '兼职'}]}

industrys = [
   {
      "code": 100000,
      "name": "互联网/AI",
      "subLevelModelList": [
         {
            "code": 100020,
            "name": "互联网",
         },
         {
            "code": 100001,
            "name": "电子商务",
         },
         {
            "code": 100021,
            "name": "计算机软件",
         },
         {
            "code": 100007,
            "name": "生活服务(O2O)",
         },
         {
            "code": 100015,
            "name": "企业服务",
         },
         {
            "code": 100006,
            "name": "医疗健康",
         },
         {
            "code": 100002,
            "name": "游戏",
         },
         {
            "code": 100003,
            "name": "社交网络与媒体",
         },
         {
            "code": 100028,
            "name": "人工智能",
         },
         {
            "code": 100029,
            "name": "云计算",
         },
         {
            "code": 100012,
            "name": "在线教育",
         },
         {
            "code": 100023,
            "name": "计算机服务",
         },
         {
            "code": 100005,
            "name": "大数据",
         },
         {
            "code": 100004,
            "name": "广告营销",
         },
         {
            "code": 100030,
            "name": "物联网",
         },
         {
            "code": 100017,
            "name": "新零售",
         },
         {
            "code": 100016,
            "name": "信息安全",
         }
      ],
   },
   {
      "code": 101400,
      "name": "电子/通信/半导体",
      "subLevelModelList": [
         {
            "code": 101405,
            "name": "半导体/芯片",
         },
         {
            "code": 101406,
            "name": "电子/硬件开发",
         },
         {
            "code": 101402,
            "name": "通信/网络设备",
         },
         {
            "code": 101401,
            "name": "智能硬件/消费电子",
         },
         {
            "code": 101403,
            "name": "运营商/增值服务",
         },
         {
            "code": 101404,
            "name": "计算机硬件",
         },
         {
            "code": 101407,
            "name": "电子/半导体/集成电路",
         }
      ],
   },
   {
      "code": 101100,
      "name": "服务业",
      "subLevelModelList": [
         {
            "code": 101101,
            "name": "餐饮",
         },
         {
            "code": 101111,
            "name": "美容",
         },
         {
            "code": 101112,
            "name": "美发",
         },
         {
            "code": 101102,
            "name": "酒店/民宿",
         },
         {
            "code": 101107,
            "name": "休闲/娱乐",
         },
         {
            "code": 101113,
            "name": "运动/健身",
         },
         {
            "code": 101114,
            "name": "保健/养生",
         },
         {
            "code": 101109,
            "name": "家政服务",
         },
         {
            "code": 101103,
            "name": "旅游/景区",
         },
         {
            "code": 101105,
            "name": "婚庆/摄影",
         },
         {
            "code": 101110,
            "name": "宠物服务",
         },
         {
            "code": 101108,
            "name": "回收/维修",
         },
         {
            "code": 101104,
            "name": "美容/美发",
         },
         {
            "code": 101106,
            "name": "其他生活服务",
         }
      ],
   },
   {
      "code": 101000,
      "name": "消费品/批发/零售",
      "subLevelModelList": [
         {
            "code": 101011,
            "name": "批发/零售",
         },
         {
            "code": 101012,
            "name": "进出口贸易",
         },
         {
            "code": 101001,
            "name": "食品/饮料/烟酒",
         },
         {
            "code": 101003,
            "name": "服装/纺织",
         },
         {
            "code": 101009,
            "name": "家具/家居",
         },
         {
            "code": 101010,
            "name": "家用电器",
         },
         {
            "code": 101002,
            "name": "日化",
         },
         {
            "code": 101006,
            "name": "珠宝/首饰",
         },
         {
            "code": 101004,
            "name": "家具/家电/家居",
         },
         {
            "code": 101013,
            "name": "其他消费品",
         }
      ],
   },
   {
      "code": 100700,
      "name": "房地产/建筑",
      "subLevelModelList": [
         {
            "code": 100704,
            "name": "装修装饰",
         },
         {
            "code": 100708,
            "name": "房屋建筑工程",
         },
         {
            "code": 100709,
            "name": "土木工程",
         },
         {
            "code": 100710,
            "name": "机电工程",
         },
         {
            "code": 100707,
            "name": "物业管理",
         },
         {
            "code": 100706,
            "name": "房地产中介/租赁",
         },
         {
            "code": 100705,
            "name": "建筑材料",
         },
         {
            "code": 100701,
            "name": "房地产开发经营",
         },
         {
            "code": 100703,
            "name": "建筑设计",
         },
         {
            "code": 100711,
            "name": "建筑工程咨询服务",
         },
         {
            "code": 100712,
            "name": "土地与公共设施管理",
         },
         {
            "code": 100702,
            "name": "工程施工",
         }
      ],
   },
   {
      "code": 100300,
      "name": "教育培训",
      "subLevelModelList": [
         {
            "code": 100303,
            "name": "培训/辅导机构",
         },
         {
            "code": 100305,
            "name": "职业培训",
         },
         {
            "code": 100301,
            "name": "学前教育",
         },
         {
            "code": 100302,
            "name": "学校/学历教育",
         },
         {
            "code": 100304,
            "name": "学术/科研",
         }
      ],
   },
   {
      "code": 100100,
      "name": "广告/传媒/文化/体育",
      "subLevelModelList": [
         {
            "code": 100104,
            "name": "文化艺术/娱乐",
         },
         {
            "code": 100105,
            "name": "体育",
         },
         {
            "code": 100101,
            "name": "广告/公关/会展",
         },
         {
            "code": 100103,
            "name": "广播/影视",
         },
         {
            "code": 100102,
            "name": "新闻/出版",
         }
      ],
   },
   {
      "code": 100900,
      "name": "制造业",
      "subLevelModelList": [
         {
            "code": 100906,
            "name": "通用设备",
         },
         {
            "code": 100907,
            "name": "专用设备",
         },
         {
            "code": 100908,
            "name": "电气机械/器材",
         },
         {
            "code": 100909,
            "name": "金属制品",
         },
         {
            "code": 100910,
            "name": "非金属矿物制品",
         },
         {
            "code": 100911,
            "name": "橡胶/塑料制品",
         },
         {
            "code": 100912,
            "name": "化学原料/化学制品",
         },
         {
            "code": 100913,
            "name": "仪器仪表",
         },
         {
            "code": 100914,
            "name": "自动化设备",
         },
         {
            "code": 100904,
            "name": "印刷/包装/造纸",
         },
         {
            "code": 100905,
            "name": "铁路/船舶/航空/航天制造",
         },
         {
            "code": 100915,
            "name": "计算机/通信/其他电子设备",
         },
         {
            "code": 100916,
            "name": "新材料",
         },
         {
            "code": 100901,
            "name": "机械设备/机电/重工",
         },
         {
            "code": 100902,
            "name": "仪器仪表/工业自动化",
         },
         {
            "code": 100903,
            "name": "原材料及加工/模具",
         },
         {
            "code": 100917,
            "name": "其他制造业",
         }
      ],
   },
   {
      "code": 100600,
      "name": "专业服务",
      "subLevelModelList": [
         {
            "code": 100601,
            "name": "咨询",
         },
         {
            "code": 100605,
            "name": "财务/审计/税务",
         },
         {
            "code": 100604,
            "name": "人力资源服务",
         },
         {
            "code": 100602,
            "name": "法律",
         },
         {
            "code": 100609,
            "name": "检测/认证/知识产权",
         },
         {
            "code": 100603,
            "name": "翻译",
         },
         {
            "code": 100608,
            "name": "其他专业服务",
         }
      ],
   },
   {
      "code": 100400,
      "name": "制药/医疗",
      "subLevelModelList": [
         {
            "code": 100402,
            "name": "医疗服务",
         },
         {
            "code": 100404,
            "name": "医美服务",
         },
         {
            "code": 100403,
            "name": "医疗器械",
         },
         {
            "code": 100405,
            "name": "IVD",
         },
         {
            "code": 100401,
            "name": "生物/制药",
         },
         {
            "code": 100406,
            "name": "医药批发零售",
         },
         {
            "code": 100407,
            "name": "医疗研发外包",
         }
      ],
   },
   {
      "code": 100800,
      "name": "汽车",
      "subLevelModelList": [
         {
            "code": 100804,
            "name": "新能源汽车",
         },
         {
            "code": 100805,
            "name": "汽车智能网联",
         },
         {
            "code": 100806,
            "name": "汽车经销商",
         },
         {
            "code": 100807,
            "name": "汽车后市场",
         },
         {
            "code": 100801,
            "name": "汽车研发/制造",
         },
         {
            "code": 100802,
            "name": "汽车零部件",
         },
         {
            "code": 100808,
            "name": "摩托车/自行车制造",
         },
         {
            "code": 100803,
            "name": "4S店/后市场",
         }
      ],
   },
   {
      "code": 100500,
      "name": "交通运输/物流",
      "subLevelModelList": [
         {
            "code": 100505,
            "name": "即时配送",
         },
         {
            "code": 100506,
            "name": "快递",
         },
         {
            "code": 100507,
            "name": "公路物流",
         },
         {
            "code": 100508,
            "name": "同城货运",
         },
         {
            "code": 100509,
            "name": "跨境物流",
         },
         {
            "code": 100510,
            "name": "装卸搬运和仓储业",
         },
         {
            "code": 100511,
            "name": "客运服务",
         },
         {
            "code": 100512,
            "name": "港口/铁路/公路/机场",
         },
         {
            "code": 100501,
            "name": "交通/运输",
         },
         {
            "code": 100502,
            "name": "物流/仓储",
         }
      ],
   },
   {
      "code": 101200,
      "name": "能源/化工/环保",
      "subLevelModelList": [
         {
            "code": 101208,
            "name": "光伏",
         },
         {
            "code": 101209,
            "name": "储能",
         },
         {
            "code": 101210,
            "name": "动力电池",
         },
         {
            "code": 101211,
            "name": "风电",
         },
         {
            "code": 101212,
            "name": "其他新能源",
         },
         {
            "code": 101207,
            "name": "环保",
         },
         {
            "code": 101202,
            "name": "化工",
         },
         {
            "code": 101205,
            "name": "电力/热力/燃气/水利",
         },
         {
            "code": 101201,
            "name": "石油/石化",
         },
         {
            "code": 101203,
            "name": "矿产/地质",
         },
         {
            "code": 101204,
            "name": "采掘/冶炼",
         },
         {
            "code": 101206,
            "name": "新能源",
         }
      ],
   },
   {
      "code": 100200,
      "name": "金融",
      "subLevelModelList": [
         {
            "code": 100206,
            "name": "互联网金融",
         },
         {
            "code": 100201,
            "name": "银行",
         },
         {
            "code": 100207,
            "name": "投资/融资",
         },
         {
            "code": 100203,
            "name": "证券/期货",
         },
         {
            "code": 100204,
            "name": "基金",
         },
         {
            "code": 100202,
            "name": "保险",
         },
         {
            "code": 100208,
            "name": "租赁/拍卖/典当/担保",
         },
         {
            "code": 100205,
            "name": "信托",
         },
         {
            "code": 100209,
            "name": "财富管理",
         },
         {
            "code": 100210,
            "name": "其他金融业",
         }
      ],
   },
   {
      "code": 101300,
      "name": "政府/非盈利机构/其他",
      "subLevelModelList": [
         {
            "code": 101303,
            "name": "农/林/牧/渔",
         },
         {
            "code": 101302,
            "name": "非盈利机构",
         },
         {
            "code": 101301,
            "name": "政府/公共事业",
         },
         {
            "code": 101304,
            "name": "其他行业",
         }
      ],
   }
]
