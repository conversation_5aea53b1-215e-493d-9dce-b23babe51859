a = [
    {
        "code": 100000,
        "name": "互联网/AI",
        "subLevelModelList": [
            {
                "code": 100020,
                "name": "互联网",
            },
            {
                "code": 100001,
                "name": "电子商务",
            },
            {
                "code": 100021,
                "name": "计算机软件",
            },
            {
                "code": 100007,
                "name": "生活服务(O2O)",

            },
            {
                "code": 100015,
                "name": "企业服务",

            },
            {
                "code": 100006,
                "name": "医疗健康",

            },
            {
                "code": 100002,
                "name": "游戏",

            },
            {
                "code": 100003,
                "name": "社交网络与媒体",

            },
            {
                "code": 100028,
                "name": "人工智能",

            },
            {
                "code": 100029,
                "name": "云计算",

            },
            {
                "code": 100012,
                "name": "在线教育",

            },
            {
                "code": 100023,
                "name": "计算机服务",

            },
            {
                "code": 100005,
                "name": "大数据",

            },
            {
                "code": 100004,
                "name": "广告营销",

            },
            {
                "code": 100030,
                "name": "物联网",

            },
            {
                "code": 100017,
                "name": "新零售",

            },
            {
                "code": 100016,
                "name": "信息安全",

            }
        ],
    },
    {
        "code": 101400,
        "name": "电子/通信/半导体",

        "subLevelModelList": [
            {
                "code": 101405,
                "name": "半导体/芯片",

            },
            {
                "code": 101406,
                "name": "电子/硬件开发",

            },
            {
                "code": 101402,
                "name": "通信/网络设备",

            },
            {
                "code": 101401,
                "name": "智能硬件/消费电子",

            },
            {
                "code": 101403,
                "name": "运营商/增值服务",

            },
            {
                "code": 101404,
                "name": "计算机硬件",

            },
            {
                "code": 101407,
                "name": "电子/半导体/集成电路",

            }
        ],

    },
    {
        "code": 101100,
        "name": "服务业",

        "subLevelModelList": [
            {
                "code": 101101,
                "name": "餐饮",

            },
            {
                "code": 101111,
                "name": "美容",

            },
            {
                "code": 101112,
                "name": "美发",

            },
            {
                "code": 101102,
                "name": "酒店/民宿",

            },
            {
                "code": 101107,
                "name": "休闲/娱乐",

            },
            {
                "code": 101113,
                "name": "运动/健身",

            },
            {
                "code": 101114,
                "name": "保健/养生",

            },
            {
                "code": 101109,
                "name": "家政服务",

            },
            {
                "code": 101103,
                "name": "旅游/景区",

            },
            {
                "code": 101105,
                "name": "婚庆/摄影",

            },
            {
                "code": 101110,
                "name": "宠物服务",

            },
            {
                "code": 101108,
                "name": "回收/维修",

            },
            {
                "code": 101104,
                "name": "美容/美发",

            },
            {
                "code": 101106,
                "name": "其他生活服务",

            }
        ],

    },
    {
        "code": 101000,
        "name": "消费品/批发/零售",

        "subLevelModelList": [
            {
                "code": 101011,
                "name": "批发/零售",

            },
            {
                "code": 101012,
                "name": "进出口贸易",

            },
            {
                "code": 101001,
                "name": "食品/饮料/烟酒",

            },
            {
                "code": 101003,
                "name": "服装/纺织",

            },
            {
                "code": 101009,
                "name": "家具/家居",

            },
            {
                "code": 101010,
                "name": "家用电器",

            },
            {
                "code": 101002,
                "name": "日化",

            },
            {
                "code": 101006,
                "name": "珠宝/首饰",

            },
            {
                "code": 101004,
                "name": "家具/家电/家居",

            },
            {
                "code": 101013,
                "name": "其他消费品",

            }
        ],

    },
    {
        "code": 100700,
        "name": "房地产/建筑",

        "subLevelModelList": [
            {
                "code": 100704,
                "name": "装修装饰",

            },
            {
                "code": 100708,
                "name": "房屋建筑工程",

            },
            {
                "code": 100709,
                "name": "土木工程",

            },
            {
                "code": 100710,
                "name": "机电工程",

            },
            {
                "code": 100707,
                "name": "物业管理",

            },
            {
                "code": 100706,
                "name": "房地产中介/租赁",

            },
            {
                "code": 100705,
                "name": "建筑材料",

            },
            {
                "code": 100701,
                "name": "房地产开发经营",

            },
            {
                "code": 100703,
                "name": "建筑设计",

            },
            {
                "code": 100711,
                "name": "建筑工程咨询服务",

            },
            {
                "code": 100712,
                "name": "土地与公共设施管理",

            },
            {
                "code": 100702,
                "name": "工程施工",

            }
        ],

    },
    {
        "code": 100300,
        "name": "教育培训",

        "subLevelModelList": [
            {
                "code": 100303,
                "name": "培训/辅导机构",

            },
            {
                "code": 100305,
                "name": "职业培训",

            },
            {
                "code": 100301,
                "name": "学前教育",

            },
            {
                "code": 100302,
                "name": "学校/学历教育",

            },
            {
                "code": 100304,
                "name": "学术/科研",

            }
        ],

    },
    {
        "code": 100100,
        "name": "广告/传媒/文化/体育",

        "subLevelModelList": [
            {
                "code": 100104,
                "name": "文化艺术/娱乐",

            },
            {
                "code": 100105,
                "name": "体育",

            },
            {
                "code": 100101,
                "name": "广告/公关/会展",

            },
            {
                "code": 100103,
                "name": "广播/影视",

            },
            {
                "code": 100102,
                "name": "新闻/出版",

            }
        ],

    },
    {
        "code": 100900,
        "name": "制造业",

        "subLevelModelList": [
            {
                "code": 100906,
                "name": "通用设备",

            },
            {
                "code": 100907,
                "name": "专用设备",

            },
            {
                "code": 100908,
                "name": "电气机械/器材",

            },
            {
                "code": 100909,
                "name": "金属制品",

            },
            {
                "code": 100910,
                "name": "非金属矿物制品",

            },
            {
                "code": 100911,
                "name": "橡胶/塑料制品",

            },
            {
                "code": 100912,
                "name": "化学原料/化学制品",

            },
            {
                "code": 100913,
                "name": "仪器仪表",

            },
            {
                "code": 100914,
                "name": "自动化设备",

            },
            {
                "code": 100904,
                "name": "印刷/包装/造纸",

            },
            {
                "code": 100905,
                "name": "铁路/船舶/航空/航天制造",

            },
            {
                "code": 100915,
                "name": "计算机/通信/其他电子设备",

            },
            {
                "code": 100916,
                "name": "新材料",

            },
            {
                "code": 100901,
                "name": "机械设备/机电/重工",

            },
            {
                "code": 100902,
                "name": "仪器仪表/工业自动化",

            },
            {
                "code": 100903,
                "name": "原材料及加工/模具",

            },
            {
                "code": 100917,
                "name": "其他制造业",

            }
        ],

    },
    {
        "code": 100600,
        "name": "专业服务",

        "subLevelModelList": [
            {
                "code": 100601,
                "name": "咨询",

            },
            {
                "code": 100605,
                "name": "财务/审计/税务",

            },
            {
                "code": 100604,
                "name": "人力资源服务",

            },
            {
                "code": 100602,
                "name": "法律",

            },
            {
                "code": 100609,
                "name": "检测/认证/知识产权",

            },
            {
                "code": 100603,
                "name": "翻译",

            },
            {
                "code": 100608,
                "name": "其他专业服务",

            }
        ],

    },
    {
        "code": 100400,
        "name": "制药/医疗",

        "subLevelModelList": [
            {
                "code": 100402,
                "name": "医疗服务",

            },
            {
                "code": 100404,
                "name": "医美服务",

            },
            {
                "code": 100403,
                "name": "医疗器械",

            },
            {
                "code": 100405,
                "name": "IVD",

            },
            {
                "code": 100401,
                "name": "生物/制药",

            },
            {
                "code": 100406,
                "name": "医药批发零售",

            },
            {
                "code": 100407,
                "name": "医疗研发外包",

            }
        ],

    },
    {
        "code": 100800,
        "name": "汽车",

        "subLevelModelList": [
            {
                "code": 100804,
                "name": "新能源汽车",

            },
            {
                "code": 100805,
                "name": "汽车智能网联",

            },
            {
                "code": 100806,
                "name": "汽车经销商",

            },
            {
                "code": 100807,
                "name": "汽车后市场",

            },
            {
                "code": 100801,
                "name": "汽车研发/制造",

            },
            {
                "code": 100802,
                "name": "汽车零部件",

            },
            {
                "code": 100808,
                "name": "摩托车/自行车制造",

            },
            {
                "code": 100803,
                "name": "4S店/后市场",

            }
        ],

    },
    {
        "code": 100500,
        "name": "交通运输/物流",

        "subLevelModelList": [
            {
                "code": 100505,
                "name": "即时配送",

            },
            {
                "code": 100506,
                "name": "快递",

            },
            {
                "code": 100507,
                "name": "公路物流",

            },
            {
                "code": 100508,
                "name": "同城货运",

            },
            {
                "code": 100509,
                "name": "跨境物流",

            },
            {
                "code": 100510,
                "name": "装卸搬运和仓储业",

            },
            {
                "code": 100511,
                "name": "客运服务",

            },
            {
                "code": 100512,
                "name": "港口/铁路/公路/机场",

            },
            {
                "code": 100501,
                "name": "交通/运输",

            },
            {
                "code": 100502,
                "name": "物流/仓储",

            }
        ],

    },
    {
        "code": 101200,
        "name": "能源/化工/环保",

        "subLevelModelList": [
            {
                "code": 101208,
                "name": "光伏",

            },
            {
                "code": 101209,
                "name": "储能",

            },
            {
                "code": 101210,
                "name": "动力电池",

            },
            {
                "code": 101211,
                "name": "风电",

            },
            {
                "code": 101212,
                "name": "其他新能源",

            },
            {
                "code": 101207,
                "name": "环保",

            },
            {
                "code": 101202,
                "name": "化工",

            },
            {
                "code": 101205,
                "name": "电力/热力/燃气/水利",

            },
            {
                "code": 101201,
                "name": "石油/石化",

            },
            {
                "code": 101203,
                "name": "矿产/地质",

            },
            {
                "code": 101204,
                "name": "采掘/冶炼",

            },
            {
                "code": 101206,
                "name": "新能源",

            }
        ],

    },
    {
        "code": 100200,
        "name": "金融",

        "subLevelModelList": [
            {
                "code": 100206,
                "name": "互联网金融",

            },
            {
                "code": 100201,
                "name": "银行",

            },
            {
                "code": 100207,
                "name": "投资/融资",

            },
            {
                "code": 100203,
                "name": "证券/期货",

            },
            {
                "code": 100204,
                "name": "基金",

            },
            {
                "code": 100202,
                "name": "保险",

            },
            {
                "code": 100208,
                "name": "租赁/拍卖/典当/担保",

            },
            {
                "code": 100205,
                "name": "信托",

            },
            {
                "code": 100209,
                "name": "财富管理",

            },
            {
                "code": 100210,
                "name": "其他金融业",

            }
        ],

    },
    {
        "code": 101300,
        "name": "政府/非盈利机构/其他",

        "subLevelModelList": [
            {
                "code": 101303,
                "name": "农/林/牧/渔",

            },
            {
                "code": 101302,
                "name": "非盈利机构",

            },
            {
                "code": 101301,
                "name": "政府/公共事业",

            },
            {
                "code": 101304,
                "name": "其他行业",

            }
        ],

    }
]
