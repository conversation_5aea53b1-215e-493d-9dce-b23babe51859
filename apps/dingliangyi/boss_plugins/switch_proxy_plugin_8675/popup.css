/* Popup styles for Switch Proxy Plugin */
body {
    width: 350px;
    min-height: 400px;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.header h2 {
    margin: 0;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.status-section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.status-item label {
    font-weight: 500;
    color: #555;
}

.status-value {
    font-weight: 600;
    margin-right: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ccc;
    transition: background-color 0.3s ease;
}

.indicator.connected {
    background: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
}

.indicator.disconnected {
    background: #f44336;
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.4);
}

.indicator.error {
    background: #ff9800;
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.4);
}

.proxy-section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-title {
    font-weight: 600;
    color: #555;
    margin-bottom: 10px;
    font-size: 14px;
}

.proxy-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border-left: 4px solid #007bff;
}

.no-proxy {
    color: #6c757d;
    font-style: italic;
}

.proxy-details {
    color: #333;
}

.proxy-details .proxy-item {
    margin: 4px 0;
    font-size: 13px;
}

.proxy-details .proxy-label {
    font-weight: 500;
    color: #555;
}

.controls-section {
    margin-bottom: 15px;
}

.btn {
    width: 100%;
    padding: 10px;
    margin: 5px 0;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.info-section {
    background: rgba(255,255,255,0.9);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.info-text {
    font-size: 12px;
    line-height: 1.4;
    color: #555;
}

.info-text p {
    margin: 8px 0;
}

.footer {
    text-align: center;
    color: rgba(255,255,255,0.8);
    font-size: 12px;
}

.version {
    font-weight: 300;
}

/* Animation for status changes */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.indicator.connected {
    animation: pulse 2s infinite;
}
