{"manifest_version": 3, "name": "Switch Proxy Plugin", "version": "1.0", "description": "A Chrome extension to switch proxy settings via WebSocket communication with Python script", "permissions": ["proxy", "storage", "activeTab", "background"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "Switch Proxy"}}