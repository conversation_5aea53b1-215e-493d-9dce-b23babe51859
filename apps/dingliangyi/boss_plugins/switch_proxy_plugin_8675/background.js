// 代理切换插件后台脚本
let websocket = null;
let currentProxy = null;
let connectionStatus = 'disconnected';
let reconnectAttempts = 0;
let maxReconnectAttempts = 10;
let reconnectInterval = null;
let heartbeatInterval = null;

// WebSocket服务器配置
const WS_SERVER_URL = 'ws://127.0.0.1:8675';

// 初始化扩展
chrome.runtime.onStartup.addListener(initializeExtension);
chrome.runtime.onInstalled.addListener(initializeExtension);

function initializeExtension() {
    console.log('代理切换插件已初始化');
    connectToWebSocket();

    // 加载保存的代理设置
    chrome.storage.local.get(['currentProxy'], (result) => {
        if (result.currentProxy) {
            currentProxy = result.currentProxy;
            console.log('已加载保存的代理设置:', currentProxy);
        }
    });
}

// 连接到WebSocket服务器
function connectToWebSocket() {
    // 清除现有的定时器
    if (reconnectInterval) {
        clearInterval(reconnectInterval);
        reconnectInterval = null;
    }
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }

    try {
        console.log(`正在尝试连接WebSocket (第 ${reconnectAttempts + 1} 次尝试)`);
        websocket = new WebSocket(WS_SERVER_URL);

        websocket.onopen = function (event) {
            console.log('WebSocket已连接到Python服务器');
            connectionStatus = 'connected';
            reconnectAttempts = 0; // 成功连接后重置重连次数
            updateConnectionStatus();

            // 启动心跳
            startHeartbeat();

            // 发送初始状态
            sendMessage({
                type: 'status',
                data: {
                    extension_ready: true,
                    current_proxy: currentProxy
                }
            });
        };

        websocket.onmessage = function (event) {
            console.log('收到消息:', event.data);
            handleWebSocketMessage(event.data);
        };

        websocket.onclose = function (event) {
            console.log('WebSocket连接已关闭, 代码:', event.code, '原因:', event.reason);
            connectionStatus = 'disconnected';
            updateConnectionStatus();

            // 停止心跳
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
            }

            // 使用指数退避算法尝试重连
            scheduleReconnect();
        };

        websocket.onerror = function (error) {
            console.error('WebSocket错误:', error);
            connectionStatus = 'error';
            updateConnectionStatus();
        };

    } catch (error) {
        console.error('连接WebSocket失败:', error);
        connectionStatus = 'error';
        updateConnectionStatus();

        // 安排重连
        scheduleReconnect();
    }
}

// 使用指数退避算法安排重连
function scheduleReconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
        console.log('已达到最大重连次数');
        return;
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // 最大30秒
    reconnectAttempts++;

    console.log(`计划在 ${delay}ms 后重连 (第 ${reconnectAttempts} 次尝试)`);

    reconnectInterval = setTimeout(() => {
        connectToWebSocket();
    }, delay);
}

// 启动心跳以保持连接
function startHeartbeat() {
    heartbeatInterval = setInterval(() => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            sendMessage({
                type: 'ping',
                data: {timestamp: Date.now()}
            });
        }
    }, 30000); // 每30秒发送一次ping
}

// 处理传入的WebSocket消息
function handleWebSocketMessage(data) {
    try {
        const message = JSON.parse(data);
        console.log('正在处理消息:', message.type, message.data);

        switch (message.type) {
            case 'switch_proxy':
                console.log('正在切换代理到:', message.data);
                switchProxy(message.data);
                break;
            case 'clear_proxy':
                console.log('正在清除代理');
                clearProxy();
                break;
            case 'get_status':
                console.log('正在获取状态');
                sendCurrentStatus();
                break;
            case 'welcome':
                console.log('收到欢迎消息');
                break;
            default:
                console.log('未知消息类型:', message.type);
        }
    } catch (error) {
        console.error('解析WebSocket消息时出错:', error);
    }
}

// 切换代理设置
function switchProxy(proxyData) {
    console.log('switchProxy被调用，参数:', proxyData);

    const config = {
        mode: 'fixed_servers',
        rules: {
            singleProxy: {
                scheme: proxyData.scheme || 'http',
                host: proxyData.host,
                port: parseInt(proxyData.port)
            }
        }
    };

    console.log('正在设置代理配置:', config);

    chrome.proxy.settings.set({
        value: config,
        scope: 'regular'
    }, function () {
        console.log('代理设置回调已执行');

        if (chrome.runtime.lastError) {
            console.error('设置代理时出错:', chrome.runtime.lastError);
            const errorResponse = {
                type: 'proxy_result',
                data: {
                    success: false,
                    error: chrome.runtime.lastError.message
                }
            };
            console.log('正在发送错误响应:', errorResponse);
            sendMessage(errorResponse);
        } else {
            currentProxy = proxyData;
            chrome.storage.local.set({currentProxy: currentProxy});
            console.log('代理设置成功:', proxyData);

            const successResponse = {
                type: 'proxy_result',
                data: {
                    success: true,
                    proxy: currentProxy
                }
            };
            console.log('正在发送成功响应:', successResponse);
            sendMessage(successResponse);
        }
    });
}

// 清除代理设置
function clearProxy() {
    chrome.proxy.settings.clear({scope: 'regular'}, function () {
        if (chrome.runtime.lastError) {
            console.error('清除代理时出错:', chrome.runtime.lastError);
        } else {
            currentProxy = null;
            chrome.storage.local.remove('currentProxy');
            console.log('代理清除成功');

            sendMessage({
                type: 'proxy_result',
                data: {
                    success: true,
                    proxy: null
                }
            });
        }
    });
}

// 发送当前状态
function sendCurrentStatus() {
    sendMessage({
        type: 'status_response',
        data: {
            connection_status: connectionStatus,
            current_proxy: currentProxy
        }
    });
}

// 发送消息到WebSocket服务器
function sendMessage(message) {
    console.log('正在尝试发送消息:', message);
    console.log('WebSocket状态:', websocket ? websocket.readyState : 'null');

    if (websocket && websocket.readyState === WebSocket.OPEN) {
        const messageStr = JSON.stringify(message);
        console.log('正在发送消息:', messageStr);
        websocket.send(messageStr);
        console.log('消息发送成功');
    } else {
        console.error('WebSocket未连接，无法发送消息。状态:',
            websocket ? websocket.readyState : 'websocket为null');
    }
}

// 更新弹出窗口的连接状态
function updateConnectionStatus() {
    chrome.storage.local.set({connectionStatus: connectionStatus});
}

// 处理来自弹出窗口的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
        case 'getStatus':
            sendResponse({
                connectionStatus: connectionStatus,
                currentProxy: currentProxy,
                reconnectAttempts: reconnectAttempts
            });
            break;
        case 'reconnect':
            // 重置重连次数并强制重连
            reconnectAttempts = 0;
            if (reconnectInterval) {
                clearInterval(reconnectInterval);
                reconnectInterval = null;
            }
            connectToWebSocket();
            sendResponse({success: true});
            break;
        case 'clearProxy':
            clearProxy();
            sendResponse({success: true});
            break;
    }
});
