import time
import json
import re
from datetime import datetime, timedelta
import requests
from zhconv import convert
from loguru import logger
from apps.dingliangyi.hk.oss import OssDB
import fitz

from apps.spider.parser.parser import ParserTask, Parser
from resx.obs_client import OBSClient
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.core.conf import ParserConf
from libs.feishu import send_feishu_message_attachments as feishu
from apps.dingliangyi.hk.prompt import text1, text2, text3

from dao.company_hk import CompanyHk
from dao.hk.hk_annual_brief import HkAnnualBrief
from dao.hk.hk_company_authorized import HkCompanyAuthorized
from dao.hk.hk_director_all_company import HkDirectorAllCompany
from dao.hk.hk_image_list import HkImageList
from dao.hk.hk_annual_capital import HkAnnualCapital
from dao.hk.hk_annual_shareholders import HkAnnualShareholders
from dao.hk.hk_company_secretary_particulars_new import HkCompanySecretaryParticularsNew
from dao.hk.hk_directors_new import HkDirectorsNew
from dao.hk.hk_pay_info import HkPayInfoDao
from apps.dingliangyi.hk.inv_id import get_gid
from resx.mysql_dao import MySQLDao
from resx.config import *

oss_db = OssDB('jindi-oss-companyinfo')


class ParserHKTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)


class ParserHK(Parser):
    def __init__(self, spider_name: str, parser_conf: ParserConf):
        self.name_en_cn_list = None
        self.HkPayInfoDao = HkPayInfoDao()
        self.obs = OBSClient(bucket_name='jindi-oss-gsxt')
        self.chat_id = 'oc_c1a20434dc7c3ca4c6fc279ddfaeb879'
        self.origin_url = 'https://jindi-oss-companyinfo.oss-cn-beijing.aliyuncs.com/channel/'
        self.CoyType = {
            ("I", "B"): '私人股份有限公司',
            ("I", "C"): '担保有限公司',
            ("I", "A"): '公众股份有限公司',
            ("U", "E"): '私人无限公司',
            ("U", "D"): '公众无限公司',
            ("F", None): '注册非香港公司',
            ("FM", None): '曾登记押记的非注册海外法团',
            ("LF", None): '有限合伙基金',
            ('LP', None): '有限责任合伙',
            ('MF', None): '互惠基金',
            ('OF', None): '开放式基金型公司',
            ('RT', None): '注册受托人法团',
            ('SO', None): '根据特别条例成立的法人团体',
            ('TC', None): '登记的信托公司',
            ('UC', None): '非注册公司',
        }
        self.actStatus = {
            'L': ('仍注册', '仍註冊'),
            'C': ('已终止营业地点', '已終止營業地點'),
            'D': ('已告解散', '已告解散'),
            'A': ('合并后不再是独立的实体', '合併後不再是獨立的實體'),
        }
        self.dao_hk = MySQLDao(db_tb_name='prism.company_hk', **CFG_MYSQL_GS_OUTER, primary_index_fields=(['br_num'], []), entity_class=CompanyHk,
                               ignore_fields=['id', 'company_id', 'updatetime'])
        self.annual_brief_dao = MySQLDao(db_tb_name='prism.hk_annual_brief', **CFG_MYSQL_GS_OUTER, primary_index_fields=(['hk_id'], []),
                                         entity_class=HkAnnualBrief, ignore_fields=['id', 'update_time'])
        self.share_dao = MySQLDao(db_tb_name='prism.hk_annual_capital', **CFG_MYSQL_GS_OUTER,
                                  primary_index_fields=([], []),
                                  entity_class=HkAnnualCapital, ignore_fields=['id', 'create_time'])
        self.shareholders_dao = MySQLDao(db_tb_name='prism.hk_annual_shareholders', **CFG_MYSQL_GS_OUTER,
                                         primary_index_fields=(['hk_id'], ['shareholder_name']),
                                         entity_class=HkAnnualShareholders, ignore_fields=['id', 'create_time'])
        self.secretary_dao = MySQLDao(db_tb_name='prism.hk_company_secretary_particulars_new', **CFG_MYSQL_GS_OUTER,
                                      primary_index_fields=(['hk_id'], ['name_english', 'name_chinese', 'cr_no', 'passport_no']),
                                      entity_class=HkCompanySecretaryParticularsNew, ignore_fields=['id', 'create_time'])
        self.directors_dao = MySQLDao(db_tb_name='prism.hk_directors_new', **CFG_MYSQL_GS_OUTER,
                                      primary_index_fields=(['hk_id'], ['name_english', 'name_chinese', 'cr_no', 'passport_no']),
                                      entity_class=HkDirectorsNew, ignore_fields=['id', 'create_time'])
        self.dao_image = MySQLDao(db_tb_name='prism.hk_image_list', **CFG_MYSQL_GS_OUTER, primary_index_fields=(['hk_id'], ['image_id_str']),
                                  entity_class=HkImageList, ignore_fields=['id', 'create_time'])
        self.director_all_company_dao = MySQLDao(db_tb_name='prism.hk_director_all_company', **CFG_MYSQL_GS_OUTER,
                                                 primary_index_fields=(['director_id', 'company_num', 'cr_no', 'passport_no'], []),
                                                 entity_class=HkDirectorAllCompany, ignore_fields=['id', 'create_time'])
        self.company_authorized_dao = MySQLDao(db_tb_name='prism.hk_company_authorized', **CFG_MYSQL_GS_OUTER,
                                               primary_index_fields=(['hk_id'], ['en_name', 'cn_name', 'cr_no', 'passport_no']),
                                               entity_class=HkCompanyAuthorized, ignore_fields=['id', 'create_time'])
        super().__init__(spider_name=spider_name, task_cls=ParserHKTask, parser_conf=parser_conf)

    def do_parse(self, *args, **kwargs):
        task: ParserHKTask = self.get_parser_task()
        eventlog = task.eventlog
        brno = eventlog.selector.word
        base_info = json.loads(task.pages['res.json'])
        try:
            hk: CompanyHk = self.dao_hk.get(br_num=brno)
            hk_id = hk.id
            F = False if base_info.get('coyTyp') != 'F' else True
            # 影像资料列表 hk_image_list
            self.image_list_parse(json.loads(task.pages['image_list.json']), hk_id, brno)
            # 股本信息 hk_share_capital
            share_capital = self.share_capital_paser(json.loads(task.pages['share_capital.json']), hk_id, F)
            # 秘书 hk_company_secretary_particulars_new
            self.secretary_parse(json.loads(task.pages['company_secretary.json']), hk_id, brno)
            # 获权
            self.auth_rep_parse(json.loads(task.pages['auth_rep.json']), hk_id, brno)

            # 董事 hk_directors_new
            directors_list = self.directors_list_parse(json.loads(task.pages.get('directors_list.json', {})), hk_id, brno)
            if type(directors_list) is list:
                for key, value in task.pages.items():
                    if key.startswith('list_individual_director_'):
                        # 董事all company信息 hk_director_all_company
                        if e := self.list_individual_director_parse(value, directors_list, brno):
                            logger.error('brno: [{}] - '.format(brno) + e)
            elif type(directors_list) is str:
                logger.error('brno: {} - '.format(brno) + str(directors_list))

            # 通过查找hk_image_list表找到最新一年的可供阅览的pdf文件image_id
            barcode = self.find_barcode(json.loads(task.pages['image_list.json']))
            if not barcode:
                return
            if not self.Manual_check(share_capital, barcode, brno, json.loads(task.pages.get('shareholder_information.json', {})), F,
                                     task.pages.get('balance.json', '余额获取失败')):
                eventlog.code = EventlogCode.FAIL
                return
        except Exception as e:
            error = self.custom_traceback(e, False)
            logger.error(f"brno: [{brno}] 解析错误 \n {error}")
            eventlog.code = EventlogCode.FAIL

    def image_list_parse(self, image_list, hk_id, brno):
        for i in image_list['01']:
            info = {
                'hk_id': hk_id,
                'image_id_str': i['docID'],
                'image_id': 0,
                'image_name': i['docChNameDisplay'],
                'image_name_s': convert(i['docChNameDisplay'], "zh-hans"),
                'filing_date': datetime.strptime(i['filingDate'], "%d-%b-%Y %H:%M") if i.get('filingDate') and i.get('filingDate') != '-' else None,
                'pages': i.get('noOfPg'),
                'size': i.get('fileSize'),
                'state': '可供查阅' if 'AVAILABLE' in i['statusForDisplay'] else '不可查阅',
                # 'image_type': i['docType']
                'image_type': '周年申報表及帳目 / 財務報表'
            }
            self.dao_image.save(HkImageList(**info))

    @staticmethod
    def share_capital_paser(share_capital, hk_id, F):
        if not share_capital or F:
            return []
        list_ = []
        for i in range(0, len(share_capital), 2):
            pair = share_capital[i:i + 2]
            info = {
                'hk_id': hk_id,
                'shares_class': '',
                'currency': pair[0]['currCode'],
                'issued_total_number': 0,
                'issued_total_amount': float(pair[0]['amt']),
                'issued_total_amount_paid': float(pair[1]['amt']) if len(pair) == 2 else ''
            }
            list_.append(info)
        return list_

    def secretary_parse(self, secretary, hk_id, brno):
        if not secretary:
            return

        secretary_list = []
        if secretary['naturalPerson']:
            for naturalPerson in secretary['naturalPerson']:
                info = {
                    'hk_id': hk_id,
                    'name_english': self.select_name(naturalPerson['engSname'], naturalPerson['engOname']),
                    'name_chinese': naturalPerson['chiName'] if naturalPerson['chiName'] else '-',
                    'pre_used_name': ';'.join(naturalPerson['previousName']) or '-',
                    'another_name': ';'.join(naturalPerson['alias']) or '-',
                    'mailing_address': naturalPerson['address'],
                    'cr_no': naturalPerson['hkid'],
                    'passport_no': naturalPerson['pptNo'],
                    'passport_iss_country': self.select_name(naturalPerson['ctryTcName'], naturalPerson['ctryEnName']),
                    'appointment': self.timestamp2datetime(naturalPerson['apptDate']),
                    'import_note': naturalPerson['rmk'] or '-',
                    'entity_type': 'naturalPerson 自然人'
                }
                secretary_list.append(HkCompanySecretaryParticularsNew.from_dict(info))
        if secretary['bodyCorporate']:
            for bodyCorporate in secretary['bodyCorporate']:
                info = {
                    'hk_id': hk_id,
                    'name_english': bodyCorporate['engCoyName'],
                    'name_chinese': bodyCorporate['chiCoyName'] if bodyCorporate['chiCoyName'] else '-',
                    'cr_no': bodyCorporate['brno'],
                    'mailing_address': bodyCorporate['address'],
                    'appointment': self.timestamp2datetime(bodyCorporate['apptDate']),
                    'import_note': '-' if bodyCorporate['rmk'] is None else bodyCorporate['rmk'],
                    'entity_type': 'bodyCorporate 法人团体'
                }
                secretary_list.append(HkCompanySecretaryParticularsNew.from_dict(info))
        self.secretary_dao.save_group(secretary_list, [hk_id])
        logger.info(f'brno: [{brno}] - {secretary_list}')

    def auth_rep_parse(self, auth_rep, hk_id, brno):
        if not auth_rep:
            return

        for auth in auth_rep['naturalPerson']:
            info = {
                'hk_id': hk_id,
                'en_name': self.select_name(auth['npEngSname'], auth['npEngOname']),
                'cn_name': auth['npChiName'] or '-',
                'address': auth['address'],
                'cr_no': auth['npHkid'],
                'passport_no': auth['npPptNo'],
                'passport_issuing_place': self.select_name(auth['ctryTcName'], auth['ctryEnName']),
                'appointment': self.timestamp2datetime(auth['npApptDate']),
                'entity_type': 'naturalPerson 自然人'
            }
            logger.info(f'brno: [{brno}] - {info}')
            self.company_authorized_dao.save(HkCompanyAuthorized(**info))

    def directors_list_parse(self, directors_list, hk_id, brno):
        if not directors_list:
            return

        directors: list[HkDirectorsNew] = []
        directors_ = []
        for director in directors_list:
            info = {
                'hk_id': hk_id,
                'name_english': director['engName'] or '-',
                'name_chinese': director['chiName'] or '-',
                'cr_no': director['hkid'] or '-',
                'passport_no': director['pptNo'] or '-',
                'passport_iss_country': f"{director['ctryTcName'] or ''} {director['ctryEnName'] or ''}".strip() or '-',
                'entity_type': '自然人' if director['directorType'] == 'I' else '法人团体'
            }
            directors_.append(info)
            directors.append(HkDirectorsNew(**info))
        self.directors_dao.save_group(directors, [hk_id])

        res_director = []
        for r in directors_:
            del r['passport_iss_country']
            del r['entity_type']
            p = self.directors_dao.get(**r)
            if p:
                director = p.model_dump()
                info = {
                    'id': director['id'],
                    'engName': director['name_english'] if director['name_english'] != '-' else '',
                    'chiName': director['name_chinese'] if director['name_chinese'] != '-' else '',
                    'crno': self.select_num(director['cr_no']) if director['cr_no'] != '-' else '',
                    'pptNo': self.select_num(director['passport_no']) if director['passport_no'] != '-' else ''
                }
                res_director.append(info)
        return res_director

    def list_individual_director_parse(self, shareholder_information, director_info, brno):
        try:
            p = re.search('var rootData =(.*?);', shareholder_information)
            shareholder_information = json.loads(p.group(1))
        except Exception as e:
            logger.warning(f'brno: [{brno}] - 未发现shareholder_information数据')
            return

        for director in director_info:
            if all([
                director['chiName'] == shareholder_information['searchData']['chiName'],
                director['crno'] == shareholder_information['searchData']['hkid'],
                director['pptNo'] == shareholder_information['searchData']['pptNo']
            ]):
                for shareholder in shareholder_information['tableData']:
                    info = {
                        'director_id': director['id'],
                        'company_num': shareholder['brno'],
                        'cr_no': director['crno'],
                        'passport_no': director['pptNo'],
                        'company_name': shareholder.get('chiCoyName', '-'),
                        'company_type': shareholder.get('engCoyName', '-'),
                        'remark': shareholder.get('scRemarks', '-')
                    }
                    logger.info(f'brno: [{brno}] - {info}')
                    self.director_all_company_dao.save(HkDirectorAllCompany(**info))
                break

    def Manual_check(self, share_capitals, barcode, brno, shareholders, F, balance):
        # ('普通股', '优先股', '遞延股', 'Non-voting deferred')
        try:
            pdf = self.obs.get_content('hk_pdf/{}.pdf'.format(barcode))
            if not pdf:
                logger.warning('brno: [{}] - 未发现该pdf文件'.format(brno))
                return True
            feishu(chat_id=self.chat_id, file=pdf, file_name='{}.pdf'.format(barcode))
            id_: CompanyHk = self.dao_hk.get(br_num=brno).model_dump()
            pdf_info = dict(self.dao_image.get(image_id_str=barcode))
            filing_date: datetime = pdf_info['filing_date'].strftime('%Y-%m-%d')

            # 股东
            shareholderList = []
            total = 0
            self.name_en_cn_list = []
            for i in shareholders['shareholderList']:
                total += int(i.get('shrHldNum'))
            for sha in shareholders['shareholderList']:
                name = sha.get('chiName') if sha.get('chiName') else ""
                name += ' ' + sha.get('engName') if sha.get('engName') else ""
                temp = {
                    'hk_id': id_['id'],
                    'shareholder_name': name.strip(),
                    'current_holding': sha.get('shrHldNum'),
                    'address': sha.get('address'),
                    'ratio': "{:.2%}".format(int(sha.get('shrHldNum')) / total),
                    'remarks': sha.get('rmks') if sha.get('rmks') else '-',
                    'shares_class': '普通股' if sha.get('clsShr') == 'Ordinary' else sha.get('clsShr')
                }
                shareholderList.append(temp)
                self.name_en_cn_list.append({'name_en': sha.get('engName') or "",
                                             'name_cn': sha.get('chiName') or ""})

            try:
                date_capital, shareholders = self.doubao_extract_by_pdf(pdf, brno, F, sum([i['issued_total_amount'] for i in share_capitals]) if not F else 0)
                if F:
                    if not date_capital.get('return_date'):
                        date_capital, share_capitals, shareholderList = self.deal_fail(brno, F, filing_date, id_, date_capital.get('return_date'),
                                                                                       share_capitals, total, shareholderList)
                    else:
                        info1 = {'hk_id': id_['id'], 'report_year': filing_date, 'return_date': date_capital['return_date']}
                        self.annual_brief_dao.save(HkAnnualBrief(**info1))
                else:
                    if not date_capital or not shareholders or len(date_capital['hk_annual_capital']) != len(share_capitals) \
                            or len(shareholders['hk_annual_shareholders']) != len(shareholderList):
                        date_capital, share_capitals, shareholderList = self.deal_fail(brno, F, filing_date, id_, date_capital.get('return_date'),
                                                                                       share_capitals, total, shareholderList)
                    else:
                        info1 = {'hk_id': id_['id'], 'report_year': filing_date, 'return_date': date_capital['return_date']}
                        for idx, _ in enumerate(share_capitals):
                            _['shares_class'] = date_capital['hk_annual_capital'][idx]['shares_class']
                            _['issued_total_number'] = date_capital['hk_annual_capital'][idx]['issued_total_number']
                        self.save_db_date_capital_shareholder(id_['id'], info1, share_capitals, shareholderList)
            except Exception as e:
                error = self.custom_traceback(e)
                logger.warning(f'brno: [{brno}] - PDF解析失败 手动处理')
                date_capital, share_capitals, shareholderList = self.deal_fail(brno, F, filing_date, id_, '', share_capitals, total, shareholderList)

            self.HkPayInfoDao.i_u_by_any(**{'hk_id': id_['id'], 'last_pay_time': datetime.now()})
            feishu(chat_id=self.chat_id, text=f'{brno} PDF解析成功!')
            # text = self.generate_text(date_capital["return_date"], share_capitals, total, shareholderList, F)
            # feishu(chat_id=self.chat_id, ats=['丁良益'], text=f'豆包解析结果!\n{text}')
            feishu(chat_id=self.chat_id, text=f'账号余额: {balance}')
            return True
        except Exception as e:
            error = self.custom_traceback(e)
            feishu(chat_id=self.chat_id, ats=['丁良益'], text=f'brno:{brno} parser错误 请手动处理\n{error}')
            return False

    @staticmethod
    def generate_text(return_date, share_capitals, total, shareholderList, F):
        if F:
            return f'{return_date}'
        text = f'{return_date if return_date else "xxx"}\n&&&\n'
        for share_capital in share_capitals:
            for value in list(share_capital.values())[1:]:
                text += f'{value if value else "xxx"}\n'
        text += '&&&' + '\n' + str(total) + '\n'
        for shareholder in shareholderList:
            try:
                del shareholder['name_en']
                del shareholder['name_cn']
            except:
                pass
            for value in list(shareholder.values())[1:]:
                text += f'{value if value else "xxx"}\n'
        return text

    def deal_fail(self, brno, F, filing_date, id_, return_date, share_capitals=None, total=None, shareholderList=None):
        feishu(chat_id=self.chat_id, ats=['丁良益'], text=f'brno:{brno} 豆包太傻了 请手动处理')
        text = self.generate_text(return_date, share_capitals, total, shareholderList, F)
        feishu(chat_id=self.chat_id, text=text)
        info1, info2, info3 = self.get_result_by_hand(id_['id'], filing_date)
        self.save_db_date_capital_shareholder(id_['id'], info1, info2, info3)
        return info1, info2, info3

    def save_db_date_capital_shareholder(self, hk_id, info1, info2, info3):
        self.annual_brief_dao.save(HkAnnualBrief(**info1))

        if info2:
            # 股权
            # 删除历史数据
            self.share_dao.execute('delete from prism.hk_annual_capital where hk_id = %s', (hk_id,))
            for info in info2:
                sql = f"insert into prism.hk_annual_capital set {', '.join(f'{f}=%s' for f in info.keys())}"
                self.share_dao.execute(sql, list(info.values()))

        if info3:
            list_ = []
            for idx, i in enumerate(info3):
                i['current_holding'] = str(i['current_holding'])
                try:
                    i['name_cn'] = self.name_en_cn_list[idx]['name_cn']
                    i['name_en'] = self.name_en_cn_list[idx]['name_en']
                    i['inv_id'] = get_gid(i['name_cn'], i['name_en'])
                except:
                    pass
                list_.append(HkAnnualShareholders(**i))
            self.shareholders_dao.save_group(info3, [hk_id])

    def get_result_by_hand(self, hk_id, report_year):
        for _ in range(60 * 60 * 24):
            resp = requests.get('https://feishu-gsxt-callback.jindidata.com/get/result?type=report')
            try:
                if resp.status_code == 200 and resp.json()['data']['report']:
                    result = resp.json()['data']['report']
                    result = result.strip().split('&&&')
                    if len(result) == 1:
                        return {'hk_id': hk_id, 'report_year': report_year, 'return_date': result[0].strip().split('\n')[0]}, [], []
                    elif len(result) == 3:
                        # 股本
                        capital = result[1].strip().split('\n')
                        capitals = []
                        for i in range(0, len(capital), 5):
                            info = {
                                'hk_id': hk_id,
                                'shares_class': capital[i],
                                'currency': capital[i + 1],
                                'issued_total_number': capital[i + 2],
                                'issued_total_amount': capital[i + 3],
                                'issued_total_amount_paid': capital[i + 4]
                            }
                            capitals.append(info)

                        # 股东
                        shareholder = result[2].strip().split('\n')
                        shareholders = []
                        total = int(float(shareholder[0].replace(',', '')))
                        for j in range(1, len(shareholder), 6):
                            temp = {
                                'hk_id': hk_id,
                                'shareholder_name': shareholder[j],
                                'current_holding': shareholder[j + 1],
                                'address': shareholder[j + 2],
                                'ratio': "{:.2%}".format(int(float(shareholder[j + 1].replace(',', ''))) / total),
                                'remarks': shareholder[j + 4],
                                'shares_class': shareholder[j + 5]
                            }
                            shareholders.append(temp)
                        return {'hk_id': hk_id, 'report_year': report_year, 'return_date': result[0].strip().split('\n')[0]}, capitals, shareholders
                    else:
                        time.sleep(1)
                        continue
                else:
                    time.sleep(1)
                    continue
            except Exception as e:
                feishu(chat_id=self.chat_id, ats=['丁良益'], text=f'请重新输入 error: {e}')
                time.sleep(1)

    def doubao_extract_by_pdf(self, pdf_binary, br_num, F, pre_capital):
        page_count = self.upload_pdf_png(pdf_binary, br_num)
        pdf_info = {}
        pdf_list = []
        pdf_list_error = []
        for i in range(1, page_count + 1):
            prompt = text1 if i == 1 else (text2 if i == 2 else text3)
            image_url = f'{self.origin_url}{br_num}_{i}.png'
            try:
                data = self.doubao_api(image_url, prompt)
            except Exception as e:
                logger.error(f'{image_url} {e}')
                continue
            if F:
                return {'return_date': data['return_date']}, {}
            if i <= 2:
                pdf_info.update(data), True
            else:
                if data and data.get('total_number') and data.get('hk_annual_shareholders'):
                    pdf_list.append(data)
                else:
                    pdf_list_error.append(data)

        # check 股本
        if not pdf_info.get('return_date') or not pdf_info.get('hk_annual_capital'):
            return pdf_info, {}
        hk_annual_capital = []
        capital_total = 0
        for j in pdf_info['hk_annual_capital']:
            if not (j.get('issued_total_number') or j.get('issued_total_amount') or j.get('issued_total_amount_paid')
                    or j.get('currency') or j.get('shares_class')):
                continue
            j['issued_total_amount'] = float(j['issued_total_amount'])
            capital_total += j['issued_total_amount']
            hk_annual_capital.append(j)
        if not hk_annual_capital or pre_capital != capital_total:
            return pdf_info, {}
        pdf_info['hk_annual_capital'] = hk_annual_capital

        # check 股东
        pdf_list_ = []
        total_number = 0
        total_number_ = 0
        for i in pdf_list:
            if i.get('total_number'):
                total_number = int(float(i['total_number'].replace(',', '')))
                i['total_number'] = total_number
            shareholders = i['hk_annual_shareholders']
            for shareholder in shareholders:
                if (shareholder.get('shareholder_name').lower() == 'nil' or shareholder.get('address').lower() == 'nil' or
                        shareholder.get('current_holding').lower() == 'nil'):
                    continue
                if shareholder.get('shareholder_name') and shareholder.get('address') and shareholder.get('current_holding'):
                    total_number_ += int(shareholder['current_holding'].replace(',', ''))
                    shareholder['current_holding'] = int(shareholder['current_holding'].replace(',', ''))
                    pdf_list_.append(shareholder)
        if total_number != total_number_:
            return pdf_info, {}
        return pdf_info, {'total_number': total_number, 'hk_annual_shareholders': pdf_list_}

    @staticmethod
    def upload_pdf_png(pdf_binary: bytes, br_num):
        pdf_document = fitz.open(stream=pdf_binary, filetype="pdf")
        for page_num in range(pdf_document.page_count):
            page = pdf_document.load_page(page_num)
            pix = page.get_pixmap(dpi=300)
            byte_stream = io.BytesIO(pix.tobytes(output="png"))
            byte_stream.seek(0)
            image_url = oss_db.put_content_to_oss(f'channel/{br_num}_{page_num + 1}.png', byte_stream.read())
            logger.info(f'image_url: {image_url}')
        return pdf_document.page_count

    @staticmethod
    def doubao_api(image_url, text):
        model = "ep-20250304182854-4gwwd"
        temperature = 0.7
        top_p = 0.9
        url = 'http://10.99.198.62:18900/doubao'
        data = json.dumps({
            'model': model,
            'messages': [{'role': 'user', 'content': [{"type": "text", "text": text}, {"type": "image_url", "image_url": {"url": image_url}}]}],
            'stream': False,
            'parameters': {'temperature': temperature, 'top_p': top_p}})
        for _ in range(20):
            try:
                res = requests.post(url, data=data, timeout=60 * 5)
                info = json.loads(res.json()['choices'][0]['message']['content'])
                logger.info(f"json: {info}")
                return info
            except Exception as e:
                logger.error(f"请求失败 {e} {_ + 1} {res.text}")
                continue

    @staticmethod
    def select_num(A):
        p = re.search(r'[\da-zA-Z]+', A)
        if p:
            return p.group(0)
        else:
            return ''

    @staticmethod
    def select_name(A, B):
        name = ''
        if A is not None:
            name += A + ' '
        if B is not None:
            name += B + ' '
        if name == '':
            name = '-'
        return name

    @staticmethod
    def time_swap(time_str):
        if time_str == '-' or time_str == '':
            return datetime(1000, 1, 1, 0, 0, 0)

        # time_format = '%Y年%m月%d日'
        time_format = "%d-%b-%Y %H:%M"
        time_obj = datetime.strptime(time_str, time_format)

        return time_obj

    @staticmethod
    def clean(s):
        if not s:
            return ''
        s = s.strip()
        s = re.sub(r'[\n\r\t：∶]', '', s)
        s = re.sub(r'\s{2,}', ' ', s)
        mo = re.fullmatch(r'[(（]([^（）]+)[）)]', s)
        return mo.group(1) if mo else s

    @staticmethod
    def timestamp2datetime(timestamp):
        if timestamp == '-' or not timestamp:
            return None
        timestamp = int(timestamp)
        if timestamp < 0:
            epoch = datetime(1970, 1, 1)
            return epoch + timedelta(seconds=timestamp // 1000) + timedelta(hours=8)
        return datetime.fromtimestamp(timestamp // 1000)

    @staticmethod
    def extract_text_in_brackets(text):
        pattern = r'\((.*?)\)'  # 正则表达式模式匹配括号内的内容
        matches = re.findall(pattern, text)  # 提取所有匹配的文本
        return matches

    @staticmethod
    def majority_letters(text):
        pattern = r'^[\u4e00-\u9fff0-9（）()\s]+$'
        return re.fullmatch(pattern, text) is not None

    @staticmethod
    def replace_bracket(name, cn_to_en=False):
        if cn_to_en:
            return name.replace('（', '(').replace('）', ')')
        return name.replace('(', '（').replace(')', '）')

    @staticmethod
    def find_barcode(image_list):
        for image_, var in image_list.items():
            for i in var:
                if i['docChName'].find('周年申報表') > -1 and 'AVAILABLE' in i['statusForDisplay']:
                    return i['docID']
            return None


if __name__ == '__main__':
    pass
